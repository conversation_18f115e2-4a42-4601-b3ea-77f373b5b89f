import axios from 'axios'
import type { AxiosResponse, AxiosInstance, InternalAxiosRequestConfig } from 'axios'
import { toast } from 'vue-sonner'

// 创建axios实例
const request: AxiosInstance = axios.create({
  // 开发环境 baseURL: '/api',
  // 生产环境 baseURL: ''
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 确保 config 不为 undefined
    if (!config) {
      throw new Error('请求配置不能为空')
    }
    // 确保 config.url 不为 undefined
    if (!config.url) {
      throw new Error('请求URL不能为空')
    }
    // 从当前浏览器url中获取token，添加至请求头
    const token = new URLSearchParams(window.location.search).get('token')
    // 如果token存在，则添加到请求头中，否则返回错误提示
    if (!token) {
      return Promise.reject('Token不能为空')
    }
    config.headers.Authorization = `Bearer ${token}`
    // 确保 headers 不为 undefined
    config.headers = config.headers || {}
    return config
  },
  error => {
    return Promise.reject(error)
  },
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data
  },
  error => {
    let message = ''
    // 处理错误响应
    if (error.response) {
      const status = error.response.status
      if (status === 401) {
        message = '未授权，请登录'
      } else if (status === 403) {
        message = '没有权限访问'
      } else if (status === 404) {
        message = '请求的资源不存在'
      } else if (status >= 500) {
        message = '服务器错误，请稍后再试'
      } else {
        message = `请求失败: ${error.response.data.message || '未知错误'}`
      }
    } else {
      message = '网络错误，请检查您的网络连接'
    }

    toast(message)

    console.error('请求失败:', error)
    return Promise.reject(error)
  },
)

export default request
