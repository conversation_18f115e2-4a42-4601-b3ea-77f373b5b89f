<script setup lang="ts">
import { watch, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
})

// 创建音频元素并设置为循环播放
const audio = new Audio('/heart-didi.mp3')
audio.loop = true

/**
 * 解锁音频播放。
 * 浏览器限制在用户与页面交互前自动播放音频。
 * 此函数通过在首次用户点击时播放并立即暂停音频来“解锁”音频。
 */
const unlockAudio = () => {
  audio.play().catch(() => {}) // 尝试播放，忽略任何错误
  audio.pause()
  // 成功解锁后，移除事件监听器，因为它只需要执行一次
  document.removeEventListener('click', unlockAudio)
  console.log('音频播放已解锁')
}

// 组件挂载时，添加一次性的点击事件监听器以解锁音频
onMounted(() => {
  document.addEventListener('click', unlockAudio, { once: true })
})

// 组件卸载时，确保停止播放并清理资源
onUnmounted(() => {
  audio.pause()
  document.removeEventListener('click', unlockAudio)
})

// 监听 open 属性的变化来控制音频播放
watch(
  () => props.open,
  newVal => {
    if (newVal) {
      console.log('尝试播放告警音')
      // play() 方法返回一个 Promise，最好进行处理
      const playPromise = audio.play()
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.error('音频播放失败，可能是因为用户尚未与页面交互:', error)
        })
      }
    } else {
      console.log('停止播放告警音')
      audio.pause()
      audio.currentTime = 0 // 将播放位置重置到开头
    }
  },
)
</script>
<template>
  <div v-if="props.open">
    <div class="warning-overlay animate-pulse">
      <div class="warning-message">警报：发生燃气泄漏，请立即处理！</div>
    </div>
  </div>
</template>
<style scoped>
.warning-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 确保不会阻止下层元素的交互 */
  z-index: 20;
  background:
    linear-gradient(
      270deg,
      rgba(191, 48, 48, 0.3) 4%,
      rgba(191, 48, 48, 0.15) 20%,
      rgba(191, 48, 48, 0) 28%,
      rgba(191, 48, 48, 0) 50%
    ),
    linear-gradient(
      90deg,
      rgba(191, 48, 48, 0.3) 4%,
      rgba(191, 48, 48, 0.15) 20%,
      rgba(191, 48, 48, 0) 28%,
      rgba(191, 48, 48, 0) 50%
    ),
    linear-gradient(180deg, rgba(191, 48, 48, 0.3) 0%, rgba(191, 48, 48, 0) 17%, rgba(191, 48, 48, 0) 100%),
    linear-gradient(0deg, rgba(191, 48, 48, 0.3) 0%, rgba(191, 48, 48, 0) 15%, rgba(191, 48, 48, 0) 100%);
}
.warning-message {
  position: absolute;
  top: 120px;
  left: 50%;
  transform: translateX(-50%);
  width: 800px;
  height: 56px;
  line-height: 56px;
  font-size: 20px;
  color: #fff;
  background:
    linear-gradient(90deg, rgba(191, 48, 48, 0) 0%, rgba(229, 57, 57, 0.75) 49%, rgba(191, 48, 48, 0) 97%),
    linear-gradient(
      90deg,
      rgba(13, 47, 115, 0) 0%,
      rgba(13, 47, 115, 0.6) 26%,
      rgba(13, 47, 115, 0.6) 73%,
      rgba(13, 47, 115, 0) 100%
    );
  text-align: center;
  backdrop-filter: blur(4px);
}
</style>
