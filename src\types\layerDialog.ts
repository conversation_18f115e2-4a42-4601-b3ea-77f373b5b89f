export interface IndustryInfo {
  id: string
  area: number[][]
}

export interface StationInfo {
  id: string
  name: string
  personInCharge: string
  address: string
  property: string
  phone: string
}

export interface PipelineInfo {
  id: string
  name: string
  pipeType: string
  industry: string
  material: string
  burialDepth: string
  commissioningTime: string
  node: string
  personInCharge: string
  phone: string
}

export interface AreaInfo {
  id: string
  title: string
  address: string
  personInCharge: string
  riskLevel: string
  phone: string
  videos: {
    url: string
  }[]
}

export interface HazardInfo {
  id: string
  title: string
  address: string
  personInCharge: string
  industry: string
  level: string
  phone: string
  videos: {
    url: string
  }[]
}
