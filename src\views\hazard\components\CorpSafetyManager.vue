<template>
  <div class="panel-container-hfHgt-hfWdt">
    <div class="panel-header">企业安全管理人员</div>
    <div class="p-4 panel-content">
      <Table class="w-full table-fixed">
        <colgroup>
          <col style="width: 30%" />
          <col style="width: 40%" />
          <col style="width: 30%" />
        </colgroup>
        <TableHeader>
          <TableRow class="border-[#99D5FF]/30 hover:bg-transparent">
            <TableHead class="font-bold text-white">姓名</TableHead>
            <TableHead class="font-bold text-white">资格类别</TableHead>
            <TableHead class="font-bold text-white">发证时间</TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div class="overflow-hidden" :style="{ height: `${8 * rowHeight}px` }" ref="tableContainerRef">
        <Table class="w-full table-fixed">
          <colgroup>
            <col style="width: 30%" />
            <col style="width: 40%" />
            <col style="width: 30%" />
          </colgroup>
          <TableBody
            :style="{
              transform: `translateY(-${scrollTop}px)`,
              transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
            }"
          >
            <TableRow
              v-for="(item, index) in scrollList"
              :key="index"
              class="border-[#99D5FF]/30 hover:bg-[#99D5FF]/30"
              :style="{ height: `${rowHeight}px` }"
              @click="handleShowDialog(item)"
            >
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.name }}</TableCell>
              <TableCell>{{ item.type }}</TableCell>
              <TableCell>{{ item.time }}</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Table,
  TableBody,
  // TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

const emit = defineEmits<{
  (e: 'click', value: any): void
}>()

const handleShowDialog = (record: any) => {
  emit('click', record)
}

const sourceData = ref([
  {
    id: '868840093847562',
    name: '杨晓龙',
    type: '运行维护人员',
    time: '2019-07-07',
  },
  {
    id: '868840015738294',
    name: '王芳',
    type: '运行维护人员',
    time: '2021-06-20',
  },
  {
    id: '868840084726193',
    name: '姜亚东',
    type: '运行维护人员',
    time: '2018-06-17',
  },
  {
    id: '868840037492856',
    name: '李宇春',
    type: '运行维护人员',
    time: '2022-06-01',
  },
  {
    id: '868840062839471',
    name: '刘国昌',
    type: '运行维护人员',
    time: '2017-05-28',
  },
  {
    id: '868840019384756',
    name: '赵文君',
    type: '运行维护人员',
    time: '2015-05-15',
  },
  {
    id: '868840074839265',
    name: '谢同瑞',
    type: '运行维护人员',
    time: '2020-05-08',
  },
  {
    id: '868840058392746',
    name: '刘淑芳',
    type: '运行维护人员',
    time: '2018-04-24',
  },
  {
    id: '868840045839276',
    name: '崔文',
    type: '运行维护人员',
    time: '2016-04-10',
  },
])

const scrollList = computed(() => [...sourceData.value, ...sourceData.value])
const tableContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const rowHeight = 40 // 每行高度为40px
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight

    if (scrollTop.value >= sourceData.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
      }, 50)
    }
  }, 3000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  startScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.addEventListener('mouseenter', stopScrolling)
    container.addEventListener('mouseleave', startScrolling)
  }
})

onUnmounted(() => {
  stopScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>
<style scoped>
@import '@/styles/index.css';
</style>
