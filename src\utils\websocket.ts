// src/utils/websocket.ts

/**
 * WebSocket 服务类，封装了连接、断开、心跳和重连逻辑。
 */
export class WebSocketService {
  private ws: WebSocket | null = null
  private readonly url: string
  private heartBeatTimer: ReturnType<typeof setInterval> | null = null
  private reconnectTimer: ReturnType<typeof setTimeout> | null = null
  private readonly reconnectInterval: number = 5000 // 重连间隔，单位：毫秒
  private readonly heartBeatInterval: number = 30000 // 心跳间隔，单位：毫秒
  private manualDisconnect: boolean = false // 是否为手动断开

  // 回调函数
  public onOpen: (() => void) | null = null
  public onMessage: ((data: any) => void) | null = null
  public onClose: (() => void) | null = null
  public onError: ((error: Event) => void) | null = null

  /**
   * @param url WebSocket 服务器地址
   */
  constructor(url: string) {
    this.url = url
  }

  /**
   * 建立 WebSocket 连接
   */
  public connect(): void {
    if (this.ws) {
      console.log('WebSocket 已连接，无需重复连接。')
      return
    }

    console.log(`正在连接到 ${this.url}...`)
    this.manualDisconnect = false
    this.ws = new WebSocket(this.url)

    this.ws.onopen = () => {
      console.log('WebSocket 连接成功。')
      this.startHeartBeat()
      if (this.onOpen) {
        this.onOpen()
      }
    }

    this.ws.onmessage = event => {
      try {
        const data = JSON.parse(event.data)
        if (this.onMessage) {
          this.onMessage(data)
        }
      } catch (error) {
        console.error('处理 WebSocket 消息失败:', error)
      }
    }

    this.ws.onerror = error => {
      console.error('WebSocket 发生错误:', error)
      if (this.onError) {
        this.onError(error)
      }
    }

    this.ws.onclose = () => {
      console.log('WebSocket 连接已关闭。')
      this.stopHeartBeat()
      this.ws = null // 重置 WebSocket 实例
      if (this.onClose) {
        this.onClose()
      }
      // 如果不是手动断开，则尝试重连
      if (!this.manualDisconnect) {
        this.reconnect()
      }
    }
  }

  /**
   * 关闭 WebSocket 连接
   */
  public disconnect(): void {
    if (this.ws) {
      console.log('正在手动关闭 WebSocket 连接...')
      this.manualDisconnect = true
      this.ws.close()
    }
    this.stopHeartBeat()
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 发送消息
   * @param message 要发送的消息
   */
  public sendMessage(message: string | object): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const data = typeof message === 'object' ? JSON.stringify(message) : message
      this.ws.send(data)
    } else {
      console.error('WebSocket 未连接，无法发送消息。')
    }
  }

  /**
   * 启动心跳机制
   */
  private startHeartBeat(): void {
    this.stopHeartBeat() // 先停止已有的心跳
    this.heartBeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        // 发送心跳消息，通常是 'ping' 或一个简单的 JSON 对象
        this.sendMessage({ type: 'heartbeat', timestamp: Date.now() })
        console.log('发送心跳包...')
      }
    }, this.heartBeatInterval)
  }

  /**
   * 停止心跳机制
   */
  private stopHeartBeat(): void {
    if (this.heartBeatTimer) {
      clearInterval(this.heartBeatTimer)
      this.heartBeatTimer = null
    }
  }

  /**
   * 尝试重新连接
   */
  private reconnect(): void {
    if (this.reconnectTimer) {
      console.log('已经在重连中，不再执行')
      return // 如果已经在重连中，则不再执行
    }
    this.reconnectTimer = setTimeout(() => {
      console.log('尝试重新连接 WebSocket...')
      this.connect()
      this.reconnectTimer = null
    }, this.reconnectInterval)
  }
}
