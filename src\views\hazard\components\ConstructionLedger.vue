<template>
  <div class="panel-container-hfHgt-hfWdt">
    <div class="panel-header">施工台账</div>
    <div class="p-4 panel-content">
      <Table class="w-full table-fixed">
        <colgroup>
          <col style="width: 60%" />
          <col style="width: 20%" />
          <col style="width: 20%" />
        </colgroup>
        <TableHeader>
          <TableRow class="border-[#99D5FF]/30 hover:bg-transparent">
            <TableHead class="font-bold text-white">项目名称</TableHead>
            <TableHead class="font-bold text-white">地址</TableHead>
            <TableHead class="font-bold text-white">详情</TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div class="overflow-hidden" :style="{ height: `${8 * rowHeight}px` }" ref="tableContainerRef">
        <Table class="w-full table-fixed">
          <colgroup>
            <col style="width: 60%" />
            <col style="width: 20%" />
            <col style="width: 20%" />
          </colgroup>
          <TableBody
            :style="{
              transform: `translateY(-${scrollTop}px)`,
              transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
            }"
          >
            <TableRow
              v-for="(item, index) in scrollList"
              :key="index"
              class="border-[#99D5FF]/30 hover:bg-[#99D5FF]/30"
              :style="{ height: `${rowHeight}px` }"
            >
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.name }}</TableCell>
              <TableCell class="text-[#99D5FF] cursor-pointer" @click="handlePosMap(item)">定位</TableCell>
              <TableCell class="text-[#99D5FF] cursor-pointer" @click="handleShowDialog(item)">查看</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Table,
  TableBody,
  // TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

const emit = defineEmits<{
  (e: 'click', value: any): void
  (e: 'pos', value: any): void
}>()

const handleShowDialog = (record: any) => {
  emit('click', record)
}

const handlePosMap = (record: any) => {
  emit('pos', record)
}

const sourceData = ref([
  {
    id: '***************',
    name: '奥威路排水改造',
    time: '2025-07-07 19:40:14',
    remark: '浓度超标告警',
    paymentMethod: 'Credit Card',
  },
  {
    id: '***************',
    name: '海棠公园改建',
    time: '2025-06-20 18:35:10',
    remark: '浓度超标告警',
    paymentMethod: 'PayPal',
  },
  {
    id: '***************',
    name: '体育主题公园操场改建',
    time: '2025-06-17 17:30:05',
    remark: '浓度超标告警',
    paymentMethod: 'Bank Transfer',
  },
  {
    id: '***************',
    name: '豪丹路与乐民街交叉口排水改造',
    time: '2025-06-01 16:25:00',
    remark: '浓度超标告警',
    paymentMethod: 'Credit Card',
  },
  {
    id: '***************',
    name: '马庄村供水管网扩建工程',
    time: '2025-05-28 15:20:55',
    remark: '浓度超标告警',
    paymentMethod: 'PayPal',
  },
  {
    id: '***************',
    name: 'S333县道扩建工程',
    time: '2025-05-15 14:15:50',
    remark: '浓度超标告警',
    paymentMethod: 'Bank Transfer',
  },
  {
    id: '***************',
    name: '乐安街电网维修',
    time: '2025-05-08 13:10:45',
    remark: '浓度超标告警',
    paymentMethod: 'Credit Card',
  },
  {
    id: '***************',
    name: '京雄快线工程保静段',
    time: '2025-04-24 12:05:40',
    remark: '浓度超标告警',
    paymentMethod: 'PayPal',
  },
  {
    id: '***************',
    name: '中核工业园小市政工程',
    time: '2025-04-10 11:00:35',
    remark: '浓度超标告警',
    paymentMethod: 'Bank Transfer',
  },
])

const scrollList = computed(() => [...sourceData.value, ...sourceData.value])
const tableContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const rowHeight = 40 // 每行高度为40px
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight

    if (scrollTop.value >= sourceData.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
      }, 50)
    }
  }, 3000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  startScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.addEventListener('mouseenter', stopScrolling)
    container.addEventListener('mouseleave', startScrolling)
  }
})

onUnmounted(() => {
  stopScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>
<style scoped>
@import '@/styles/index.css';
</style>
