<template>
  <div class="dashboard-layout">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <div class="flex gap-6">
        <SurroundingVideo />
        <HazardSource />
      </div>
      <HazardReport />
    </div>

    <!-- 中央地图区域 -->
    <div class="center-panel">
      <!-- 地图控制组件 -->
      <LayersTool v-model="layersData" />
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <div class="flex gap-6">
        <LevelRatio />
        <CorpSafetyManager />
      </div>
      <div class="flex gap-6">
        <CorpHazardRank />
        <ConstructionLedger @click="onShowBuildingDialog" @pos="onShowMapPoint" />
      </div>
    </div>

    <!-- UE弹窗 -->
    <HazardDialog :open="hazardInfoOpen" @close="hazardInfoOpen = false" />
    <PipelineDialog :open="pipelineInfoOpen" @close="pipelineInfoOpen = false" />
    <AreaDialog :open="areaInfoOpen" @close="areaInfoOpen = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import SurroundingVideo from './components/SurroundingVideo.vue'
import HazardSource from './components/HazardSource.vue'
import HazardReport from './components/HazardReport.vue'
import LevelRatio from './components/LevelRatio.vue'
import CorpSafetyManager from './components/CorpSafetyManager.vue'
import CorpHazardRank from './components/CorpHazardRank.vue'
import ConstructionLedger from './components/ConstructionLedger.vue'
import LayersTool from '@/components/LayersTool.vue'
import HazardDialog from '@/components/ue-dialog/HazardDialog.vue'
import PipelineDialog from '@/components/ue-dialog/PipelineDialog.vue'
import AreaDialog from '@/components/ue-dialog/AreaDialog.vue'
import { sendToUe } from '@/utils/ue'
import { layerData } from './layerData'

// 弹窗状态
import type { Ref } from 'vue'
import type { HazardInfo, PipelineInfo, AreaInfo } from '@/types/layerDialog'

const hazardInfoOpen = ref(false)
const hazardInfoData: Ref<HazardInfo | null> = ref(null)
const pipelineInfoOpen = ref(false)
const pipelineInfoData: Ref<PipelineInfo | null> = ref(null)
const areaInfoOpen = ref(false)
const areaInfoData: Ref<AreaInfo | null> = ref(null)

const showBuildingDialog = ref<boolean>(false)
const buildingInfo = ref<any>({})

// 图层数据
const layersData = ref(layerData)

const onShowBuildingDialog = (record: any) => {
  console.log('building record:', record)
  buildingInfo.value = record
  showBuildingDialog.value = true
}

const onShowMapPoint = (record: any) => {
  console.log('map point record:', record)
  sendToUe({ command: 'ShowMapPoint', args: { record } })
}

// 模拟API调用
const fetchHazardInfo = async (id: string) => {
  console.log('Fetching station info for id:', id)
  await new Promise(resolve => setTimeout(resolve, 200))
  return {
    id,
    title: `危险源 ${id}`,
    personInCharge: '李**',
    address: '模拟地址',
    level: '二级',
    industry: '模拟燃气公司',
    phone: '13800138000',
    videos: [{ url: 'https://example.com/video.mp4' }],
  }
}

const fetchPipelineInfo = async (id: string) => {
  console.log('Fetching pipeline info for id:', id)
  await new Promise(resolve => setTimeout(resolve, 200))
  return {
    id,
    name: `管线 ${id}`,
    pipeType: '中压',
    industry: '模拟燃气公司',
    material: 'PE',
    burialDepth: '1.5m',
    commissioningTime: '2022-01-01',
    node: 'NodeA-NodeB',
    personInCharge: '王**',
    phone: '13900139000',
  }
}

const fetchAreaInfo = async (id: string) => {
  console.log('Fetching area info for id:', id)
  await new Promise(resolve => setTimeout(resolve, 200))
  return {
    id,
    title: `风险区域 ${id}`,
    address: '模拟风险地址',
    personInCharge: '赵**',
    riskLevel: '二级风险',
    phone: '13700137000',
    videos: [{ url: 'https://example.com/video.mp4' }],
  }
}

// UE交互处理
const handleUeClickPoint = async (data: { id: string }) => {
  const { id } = data
  const idNumber = parseInt(id.replace(/\D/g, ''), 10)
  // 假设以 'station' 开头的ID为场站，否则为企业
  if (idNumber >= 12210 && idNumber <= 12220) {
    hazardInfoData.value = await fetchHazardInfo(id)
    hazardInfoOpen.value = true
  }
}

const handleUeClickLine = async (data: { id: string }) => {
  pipelineInfoData.value = await fetchPipelineInfo(data.id)
  pipelineInfoOpen.value = true
}

const handleUeClickPlant = async (data: { id: string }) => {
  areaInfoData.value = await fetchAreaInfo(data.id)
  areaInfoOpen.value = true
}

onMounted(() => {
  // 将处理函数挂载到 window.ue.interface
  if (window.ue && window.ue.interface) {
    window.ue.interface.clickpoint = handleUeClickPoint
    window.ue.interface.clickline = handleUeClickLine
    window.ue.interface.clickplant = handleUeClickPlant
  } else {
    console.error('UE interface is not available.')
    // for debug
    window.ue = {
      interface: {
        broadcast: () => {},
      },
    }
    window.ue.interface.clickpoint = handleUeClickPoint
    window.ue.interface.clickline = handleUeClickLine
    window.ue.interface.clickplant = handleUeClickPlant
  }
})

onUnmounted(() => {
  // 清理
  if (window.ue && window.ue.interface) {
    delete window.ue.interface.clickpoint
    delete window.ue.interface.clickline
    delete window.ue.interface.clickplant
  }
  // 清除所有图层
  sendToUe({ command: 'RemoveAll', args: {} })
})
</script>

<style scoped>
.dashboard-layout {
  position: absolute;
  top: 121px;
  left: 24px;
  right: 24px;
  height: calc(100% - 121px);
  overflow: hidden;
}

.left-panel,
.right-panel {
  position: absolute;
  top: 0;
  width: 984px;
}

.left-panel {
  left: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.right-panel {
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.center-panel {
  position: absolute;
  top: 0;
  left: 1008px;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    padding: 20px;
    padding-top: 120px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 280px 1fr 280px;
    gap: 16px;
    padding: 16px;
    padding-top: 120px;
  }
}
</style>
