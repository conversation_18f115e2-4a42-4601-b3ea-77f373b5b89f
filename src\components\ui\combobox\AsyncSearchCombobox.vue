<template>
  <div class="relative">
    <div class="relative w-full">
      <input
        type="text"
        :placeholder="placeholder"
        :value="inputValue"
        @input="handleInput"
        @focus="handleFocus"
        class="text-sm dropdown-btn w-full"
        :class="{ 'opacity-50': isLoading }"
      />
      <div v-if="isLoading" class="absolute right-2 top-1/2 -translate-y-1/2">
        <div class="animate-spin h-4 w-4 border-2 border-[#99D5FF] border-t-transparent rounded-full"></div>
      </div>
      <div v-else-if="isOpen" class="absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer" @click="isOpen = false">
        <ChevronUp class="h-4 w-4 text-[#99D5FF]" />
      </div>
      <div v-else class="absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer" @click="isOpen = true">
        <ChevronDown class="h-4 w-4 text-[#99D5FF]" />
      </div>
    </div>

    <div v-if="isOpen" class="absolute z-50 mt-1 w-full bg-[#1E293B] border border-[#99D5FF] rounded shadow-md">
      <div v-if="filteredOptions.length === 0 && !isLoading" class="p-2 text-[#99D5FF] text-sm">
        {{ noResultsText }}
      </div>
      <ul v-else class="max-h-60 overflow-auto">
        <li
          v-for="option in filteredOptions"
          :key="option.value"
          @click="selectOption(option)"
          class="p-2 text-[#99D5FF] text-sm hover:bg-[#2A384D] cursor-pointer"
          :class="{ 'bg-[#2A384D]': modelValue === option.value }"
        >
          {{ option.label }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { ChevronDown, ChevronUp } from 'lucide-vue-next'

interface Option {
  label: string
  value: string
}

const props = defineProps<{
  modelValue: string
  options?: Option[]
  placeholder?: string
  noResultsText?: string
  searchFn?: (query: string) => Promise<Option[]>
  debounceMs?: number
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'search', value: string): void
}>()

const inputValue = ref('')
const isOpen = ref(false)
const isLoading = ref(false)
const searchResults = ref<Option[]>([])
const searchTimeout = ref<number | null>(null)

// 初始化选中值的显示
onMounted(() => {
  if (props.modelValue && props.options) {
    const selectedOption = props.options.find(opt => opt.value === props.modelValue)
    if (selectedOption) {
      inputValue.value = selectedOption.label
    }
  }
})

// 清理定时器
onUnmounted(() => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
})

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && props.options) {
    const selectedOption = props.options.find(opt => opt.value === newValue)
    if (selectedOption) {
      inputValue.value = selectedOption.label
    }
  }
})

// 监听options变化
watch(() => props.options, (newOptions) => {
  if (props.modelValue && newOptions) {
    const selectedOption = newOptions.find(opt => opt.value === props.modelValue)
    if (selectedOption) {
      inputValue.value = selectedOption.label
    }
  }
})

const filteredOptions = computed(() => {
  if (props.searchFn) {
    return searchResults.value
  }
  
  if (!props.options) return []
  
  if (!inputValue.value) {
    return props.options
  }
  
  return props.options.filter(option => 
    option.label.toLowerCase().includes(inputValue.value.toLowerCase())
  )
})

const handleInput = (e: Event) => {
  const target = e.target as HTMLInputElement
  inputValue.value = target.value
  isOpen.value = true
  
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  
  if (props.searchFn) {
    isLoading.value = true
    
    // 使用setTimeout实现防抖
    searchTimeout.value = window.setTimeout(async () => {
      try {
        emit('search', inputValue.value)
        searchResults.value = await props.searchFn!(inputValue.value)
      } catch (error) {
        console.error('Search error:', error)
        searchResults.value = []
      } finally {
        isLoading.value = false
      }
    }, props.debounceMs || 300)
  }
}

const handleFocus = () => {
  isOpen.value = true
}

const selectOption = (option: Option) => {
  inputValue.value = option.label
  emit('update:modelValue', option.value)
  isOpen.value = false
}

// 点击外部关闭下拉框
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.dropdown-btn {
  border: 1px solid #99D5FF;
  color: #99D5FF;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
  background: transparent;
}
</style>