<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogHeader,
  DialogTitle,
  // DialogClose,
} from '@/components/ui/dialog'

const props = defineProps<{
  open: boolean
  data?: {
    id: string
    title: string
    address: string
    personInCharge: string
    riskLevel: string
    phone: string
    videos: {
      url: string
    }[]
  } | null
}>()
const emit = defineEmits<{
  (e: 'close'): void
}>()

const data = ref(
  props.data ?? {
    id: 'area-001',
    title: '容城镇储备站高风险区域',
    address: '容城镇容易线与顺达街交叉口',
    personInCharge: '张*',
    riskLevel: '一级风险',
    phone: '19997631216',
    videos: [{ url: '/mock/stream.webm' }, { url: '/mock/stream2.webm' }, { url: '/mock/stream3.webm' }],
  },
)

watch(
  () => props.data,
  newData => {
    if (newData) {
      data.value = newData
    }
  },
)

// 处理更新打开状态的事件
const handleUpdateOpen = (open: boolean) => {
  // 当接收到 open 为 false 的更新时，触发 close 事件
  if (!open) {
    emit('close')
  }
}
</script>
<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[800px] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none"
    >
      <DialogHeader>
        <DialogTitle class="h-12 text-white pl-9 leading-12 title-bg">重大危险源详情</DialogTitle>
        <DialogDescription>
          <div class="flex">
            <div class="flex flex-col gap-5 pt-6 text-white">
              <p class="flex">
                <span class="block w-[8em] text-right text-[#99D5FF]">危险源名称：</span>
                {{ data.title }}
              </p>
              <p class="flex">
                <span class="block w-[8em] text-right text-[#99D5FF]">所属企业：</span>
                {{ data.personInCharge }}
              </p>
              <p class="flex">
                <span class="block w-[8em] text-right text-[#99D5FF]">危险源等级：</span>
                {{ data.riskLevel }}
              </p>
              <p class="flex">
                <span class="block w-[8em] text-right text-[#99D5FF]">区域监控：</span>
                <span class="cursor-pointer text-[#99D5FF]">刷新</span>
              </p>
            </div>
            <div class="flex flex-col gap-5 pt-6 text-white">
              <p class="flex">
                <span class="block w-[8em] text-right text-[#99D5FF]">地址：</span>
                {{ data.address }}
              </p>
              <p class="flex">
                <span class="block w-[8em] text-right text-[#99D5FF]">负责人电话：</span>
                {{ data.phone }}
              </p>
              <p class="flex">
                <span class="block w-[8em] text-right text-[#99D5FF]">监管负责人：</span>
                {{ data.personInCharge }}
              </p>
            </div>
          </div>
        </DialogDescription>
      </DialogHeader>
      <div class="flex justify-between p-6">
        <div class="w-[245px] h-[184px]" v-for="video in data.videos" :key="video.url">
          <video
            class="object-fill w-full h-full"
            :src="video.url"
            disablepictureinpicture
            muted
            loop
            autoplay
            playsinline
          ></video>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>
<style scoped>
.title-bg {
  background:
    url('@/assets/dialog/title-bg-800.png') no-repeat center,
    linear-gradient(270deg, rgba(71, 235, 235, 0) 0%, rgba(71, 235, 235, 0) 60%, rgba(71, 235, 235, 0.3) 100%),
    linear-gradient(90deg, rgba(119, 168, 217, 0.3) 5%, rgba(105, 141, 191, 0.3) 80%, rgba(105, 141, 191, 0.3) 100%);
  background-size: cover;
  font-family: MStiffHei PRC;
}
</style>
