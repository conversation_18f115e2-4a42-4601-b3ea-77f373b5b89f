<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import dayjs from 'dayjs'
import type { AlarmInfo } from '@/types/alarmDialog.ts'

const props = defineProps<{
  open: boolean
  data?: AlarmInfo | null
}>()
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'success'): void
}>()

const data = ref(props.data)

// 默认告警回溯数据
const defaultAlarmTracebacks = [
  { type: '浓度超标', time: '2025-07-30 16:42:30' },
  { type: '浓度超标', time: '2025-08-02 16:42:30' },
  { type: '浓度超标', time: '2025-08-05 16:42:30' },
  { type: '浓度超标', time: '2025-08-06 16:42:30' },
  { type: '浓度超标', time: '2025-08-10 16:42:30' },
  { type: '浓度超标', time: '2025-08-12 16:42:30' },
]
const alarmTracebacks = ref([...defaultAlarmTracebacks])

// 监听弹窗打开状态
watch(
  () => props.open,
  isOpen => {
    // 弹窗打开时，根据传入的 data 更新状态
    if (isOpen) {
      data.value = props.data
      // 如果是待处理事件(status===0)，则在回溯时间线中追加一条最新的节点
      if (props.data && props.data.status === 0) {
        alarmTracebacks.value.push({
          type: '浓度超标',
          time: dayjs(props.data.timestamp).format('YYYY-MM-DD HH:mm:ss'),
        })
      }
    }
  },
)
// 处理更新打开状态的事件
const handleUpdateOpen = (open: boolean) => {
  // 当接收到 open 为 false 的更新时，触发 close 事件
  if (!open) {
    // 关闭弹窗时重置时间轴数据
    alarmTracebacks.value = [...defaultAlarmTracebacks]
    emit('close')
  }
}

// 处理处置事件
const handleDispose = () => {
  emit('success')
}
</script>
<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[1200px] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none"
    >
      <DialogHeader>
        <DialogTitle class="h-12 text-white pl-9 leading-12 title-bg">告警详情</DialogTitle>
        <DialogDescription as="div" class="p-6 text-sm text-white">
          <!-- 告警详情 -->
          <div>
            <div class="mb-4 text-lg text-[#99D5FF] font-bold">告警详情</div>
            <div class="grid grid-cols-4 gap-4 mb-4">
              <div class="flex">
                <div class="text-[#99D5FF]">设备编号：</div>
                <div>{{ data?.iMEI || '4846927253887' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">设备名称：</div>
                <div>家用可燃气体探测器</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">告警类型：</div>
                <div>浓度超标</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">告警时间：</div>
                <div>{{ dayjs(data?.timestamp).format('YYYY-MM-DD HH:mm:ss') || '2025-07-08' }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">用户姓名：</div>
                <div>张思德</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">地址：</div>
                <div>电信产业园B1大厅</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">联系方式：</div>
                <div>19903330600</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">燃气用户号：</div>
                <div>1301224986</div>
              </div>
            </div>
          </div>
          <!-- 告警回溯 -->
          <div>
            <div class="mb-4 text-lg text-[#99D5FF] font-bold">告警回溯</div>
            <div class="relative mb-6">
              <!-- 时间轴线 -->
              <div class="absolute top-11 left-0 right-0 h-[4px] bg-[#99D5FF]/50"></div>
              <!-- 时间轴节点 -->
              <div class="relative flex justify-between">
                <div v-for="(item, index) in alarmTracebacks" :key="index" class="flex flex-col items-center">
                  <div class="text-[#99D5FF] mb-2">{{ item.type }}</div>
                  <div class="w-10 h-10 rounded-full bg-[#99D5FF] z-10 text-center leading-10">报警</div>
                  <div class="mt-2 text-[#99D5FF] text-xs text-center">
                    <div>{{ item.time.split(' ')[0] }}</div>
                    <div>{{ item.time.split(' ')[1] }}</div>
                  </div>
                </div>
              </div>
              <!-- 箭头 -->
            </div>
            <Button
              class="mb-4 w-[280px] mx-auto block bg-[#77A8D9] rounded-sm hover:bg-[#77A8D9]/80 text-white outline-none"
              v-if="data?.status === 0"
              @click="handleDispose"
            >
              一键处置
            </Button>
          </div>
          <!-- 处置反馈 0:待处理 1:处理中 2:已处理 -->
          <div v-if="data?.status !== 1">
            <div class="mb-4 text-lg text-[#99D5FF] font-bold">处置反馈</div>
            <div class="grid grid-cols-4 gap-4">
              <div class="flex">
                <div class="text-[#99D5FF]">处置人：</div>
                <div>王文</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">提交时间：</div>
                <div>2025-07-08</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">告警原因：</div>
                <div>软管老化</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">描述：</div>
                <div>无</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">处置方式：</div>
                <div>更换螺纹管</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] whitespace-nowrap">处置取证：</div>
                <div>
                  <div class="flex gap-2">
                    <img
                      src="https://img1.baidu.com/it/u=475744281,3916313639&fm=253&app=138&f=JPEG?w=800&h=1067"
                      alt="处置图片"
                      class="rounded w-30 h-30"
                    />
                    <img
                      src="https://pic.rmb.bdstatic.com/bjh/3f13695da2e/250422/b7919057ce453d11734d024ac413fce7.jpeg"
                      alt="处置图片"
                      class="object-cover rounded w-30 h-30"
                    />
                    <img
                      src="https://img0.baidu.com/it/u=671753217,4078023515&fm=253&app=138&f=JPEG?w=800&h=1062"
                      alt="处置图片"
                      class="object-cover rounded w-30 h-30"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogDescription>
      </DialogHeader>
    </DialogContent>
  </Dialog>
</template>
<style scoped>
.title-bg {
  background: url('@/assets/dialog/title-bg-1200.png') no-repeat 0 0;
  background-size: cover;
  font-family: MStiffHei PRC;
}
.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  outline: none;
}
</style>
