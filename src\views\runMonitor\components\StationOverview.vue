<template>
  <div class="panel-container-half">
    <div class="panel-header">站点概况</div>
    <div class="p-4 panel-content">
      <div class="flex flex-wrap status-indicators gap-y-1">
        <div v-for="(i, index) in statusData" :key="index" class="status-item animate-pulse" :class="i.icon">
          <div class="status-value">{{ i.value }}</div>
          <div class="status-label">{{ i.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

const statusData = ref([
  { label: '场站总数', value: 41, icon: 'blue' },
  { label: '门站数量', value: 9, icon: 'green' },
  { label: '调压站数量', value: 16, icon: 'yellow' },
  { label: '充装站', value: 2, icon: 'blue' },
  { label: '储备站', value: 10, icon: 'green' },
  { label: 'CNG加气站', value: 4, icon: 'yellow' },
])
</script>

<style scoped>
@import '@/styles/index.css';

.panel-content {
  height: calc(100% - 44px);
}

.status-indicators {
  width: 448px;
  height: 216px;
}

.status-item {
  display: flex;
  width: 33.3%;
  height: 50%;
  flex-direction: column;
  justify-content: space-between;
}
.status-item.blue {
  background: url('@/assets/run-monitor/station-overview-blue.png') no-repeat center 8px;
  background-size: 64px;
}
.status-item.yellow {
  background: url('@/assets/run-monitor/station-overview-green.png') no-repeat center 8px;
  background-size: 64px;
}
.status-item.green {
  background: url('@/assets/run-monitor/station-overview-yellow.png') no-repeat center 8px;
  background-size: 64px;
}

.status-value {
  font-family: NotoSansSC;
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  padding-top: 26px;
  text-align: center;
  color: #ffffff;
  white-space: nowrap;
}

.status-label {
  font-family: NotoSansSC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  padding-bottom: 4px;
  text-align: center;
  letter-spacing: normal;
  color: #ffffff;
}
</style>
