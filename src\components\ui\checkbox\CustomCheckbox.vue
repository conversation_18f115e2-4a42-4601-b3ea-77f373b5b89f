<template>
  <Checkbox
    :id="id"
    :model-value="modelValue"
    @click.prevent="handleChange"
    class="peer h-4 w-4 shrink-0 rounded-xs border focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:border-[#FFD966] data-[state=checked]:bg-[#FFD966] data-[state=indeterminate]:border-[#FFD966]! data-[state=indeterminate]:bg-white! border-[#99D5FF] text-transparent"
  >
    <div class="flex items-center justify-center">
      <div v-if="modelValue === 'indeterminate'" class="w-2 h-0.5 bg-white"></div>
      <Check v-else class="w-3 h-3 font-bold text-white" />
    </div>
  </Checkbox>
</template>

<script setup lang="ts">
import { Checkbox } from '@/components/ui/checkbox'
import { Check } from 'lucide-vue-next'

const props = defineProps<{
  id?: string
  modelValue: boolean | 'indeterminate'
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'change': [value: boolean]
}>()

const handleChange = () => {
  const newValue = props.modelValue === true ? false : true;
  // console.log('CustomCheckbox handleChange triggered. New value:', newValue);
  emit('update:modelValue', newValue);
  emit('change', newValue);
}
</script>
