<script setup lang="ts">
import { watch, computed } from 'vue'
import CustomCheckbox from '@/components/ui/checkbox/CustomCheckbox.vue'
import { sendToUe } from '@/utils/ue'

// 辅助函数：发送UE数据
function sendUeData(data: UeData | UeData[]) {
  if (Array.isArray(data)) {
    data.forEach((item: UeData) => {
      sendToUe(item)
    })
  } else {
    sendToUe(data)
  }
}

// 定义UE命令的数据结构
interface UeData {
  command: string
  args: Record<string, any>
}

interface LayerItem {
  id: string
  label: string
  checked: boolean
  ueData?: UeData[] // 添加可选的UE数据字段
  ueRemoveData?: UeData // 添加可选的UE移除数据字段
}

interface LayerGroup {
  label: string
  icon: string
  checked: boolean | 'indeterminate'
  children?: LayerItem[] // 将 children 属性改为可选
  ueData?: UeData[] // 添加可选的UE数据字段
  ueRemoveData?: UeData // 添加可选的UE移除数据字段
}

interface Layers {
  [key: string]: LayerGroup
}

const props = defineProps<{
  modelValue: Layers
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: Layers): void
}>()

const localLayers = computed({
  get: () => props.modelValue,
  set: value => {
    emit('update:modelValue', value)
  },
})

// 发送消息给UE

// 监听图层变化并发送消息给UE
watch(
  () => JSON.parse(JSON.stringify(props.modelValue)),
  (newLayers, oldLayers) => {
    if (!oldLayers) return // 首次触发时 oldLayers 为 undefined

    for (const groupName in newLayers) {
      if (Object.prototype.hasOwnProperty.call(newLayers, groupName)) {
        const newGroup = newLayers[groupName as keyof Layers]
        const oldGroup = oldLayers[groupName as keyof Layers]

        if (newGroup && oldGroup) {
          // 如果没有子项且有ue交互，则发送ue交互
          if (!newGroup.children && newGroup.ueData) {
            if (newGroup.checked !== oldGroup.checked) {
              // 只有当checked状态改变时才发送
              if (newGroup.checked) {
                if (newGroup.ueData) {
                  // 添加条件检查
                  sendUeData(newGroup.ueData)
                }
              } else {
                if (newGroup.ueRemoveData) {
                  sendToUe(newGroup.ueRemoveData)
                }
              }
            }
            continue // 处理完组的UE交互后，跳过子项处理
          }

          // 否则处理子项的变化
          if (newGroup.children && oldGroup.children) {
            // 确保 children 存在
            for (let i = 0; i < newGroup.children.length; i++) {
              const newChild = newGroup.children[i]
              const oldChild = oldGroup.children[i]

              if (newChild.checked !== oldChild.checked) {
                const originalChild = newGroup.children[i]
                if (newChild.checked) {
                  if (originalChild.ueData) {
                    // 添加条件检查
                    sendUeData(originalChild.ueData)
                  }
                } else {
                  if (originalChild.ueRemoveData) {
                    sendToUe(originalChild.ueRemoveData)
                  }
                }
              }
            }
          }
        }
      }
    }
  },
)

// 切换组的选中状态时，同步更新子项的状态
const toggleGroup = (groupName: keyof Layers) => {
  const newLayers = { ...localLayers.value } // 创建新副本
  const group = newLayers[groupName]
  if (!group) return
  const newCheckedState = group.checked

  // 如果没有子项，直接返回
  if (!group.children) return

  group.children.forEach((child: LayerItem) => {
    child.checked = newCheckedState === 'indeterminate' ? true : newCheckedState
  })
  localLayers.value = newLayers // 赋值新副本
}

// 处理子项变化
const handleChildChange = (groupName: keyof Layers) => {
  const newLayers = { ...localLayers.value } // 创建新副本
  const group = newLayers[groupName]
  if (!group || !group.children) return // 确保 group 和 children 存在
  const allChildrenChecked = group.children.every((child: LayerItem) => child.checked)
  const someChildrenChecked = group.children.some((child: LayerItem) => child.checked)

  if (allChildrenChecked) {
    group.checked = true
  } else if (someChildrenChecked) {
    group.checked = 'indeterminate'
  } else {
    group.checked = false
  }
  localLayers.value = newLayers // 赋值新副本
}
</script>
<template>
  <div class="w-[154px] p-4 wrapper">
    <div class="flex flex-col gap-2">
      <div class="layer-group" v-for="(group, groupName) in localLayers" :key="String(groupName)">
        <div class="flex items-center space-x-2">
          <CustomCheckbox
            :id="String(groupName)"
            v-model="group.checked"
            @update:model-value="toggleGroup(String(groupName))"
          />
          <label
            :for="String(groupName)"
            class="flex items-center gap-2 text-sm font-medium leading-none cursor-pointer select-none"
            :style="{ color: group.checked === false ? '#99D5FF' : '#FFD966' }"
          >
            <div :class="['checkbox-icon', group.icon, { active: group.checked }]"></div>
            {{ group.label }}
          </label>
        </div>
        <div class="pl-6 mt-2 space-y-2">
          <div class="flex items-center space-x-2" v-for="item in group.children" :key="item.id">
            <CustomCheckbox
              :id="item.id"
              v-model="item.checked"
              @update:model-value="handleChildChange(String(groupName))"
            />
            <label
              :for="item.id"
              class="text-sm leading-none cursor-pointer select-none"
              :style="{ color: item.checked ? '#FFD966' : '#99D5FF' }"
            >
              {{ item.label }}
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
.wrapper {
  background: url('@/assets/map/layers-tool-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
}

.checkbox-icon {
  width: 16px;
  height: 16px;
  background-size: 100% 100%;

  &.industry {
    background: url('@/assets/map/industry-icon.png') no-repeat center;
  }

  &.pipeline {
    background: url('@/assets/map/pipeline-icon.png') no-repeat center;
  }

  &.station {
    background: url('@/assets/map/station-icon.png') no-repeat center;
  }

  &.warning {
    background: url('@/assets/map/warning-icon.png') no-repeat center;
  }

  &.fallout {
    background: url('@/assets/map/fallout-icon.svg') no-repeat center;
  }

  &.building {
    background: url('@/assets/map/building-icon.svg') no-repeat center;
  }

  &.industry.active {
    background: url('@/assets/map/active-industry-icon.png') no-repeat center;
  }

  &.pipeline.active {
    background: url('@/assets/map/active-pipeline-icon.png') no-repeat center;
  }

  &.station.active {
    background: url('@/assets/map/active-station-icon.png') no-repeat center;
  }

  &.warning.active {
    background: url('@/assets/map/active-warning-icon.png') no-repeat center;
  }

  &.fallout.active {
    background: url('@/assets/map/fallout-icon.svg') no-repeat center;
  }

  &.building.active {
    background: url('@/assets/map/building-icon.svg') no-repeat center;
  }
}

.layer-group {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
  color: #99d5ff;
}

.layer-group:last-child {
  border-bottom: none;
}
</style>
