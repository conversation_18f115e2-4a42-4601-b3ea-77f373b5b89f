<template>
  <div class="p-8 bg-gray-900 min-h-screen">
    <h1 class="text-white text-2xl mb-4">LayersTool 测试页面</h1>
    
    <div class="flex gap-8">
      <!-- 组件测试 -->
      <div class="bg-gray-800 p-4 rounded">
        <h2 class="text-white text-lg mb-4">LayersTool 组件</h2>
        <LayersTool v-model="testLayersData" />
      </div>
      
      <!-- 调试信息 -->
      <div class="bg-gray-800 p-4 rounded flex-1">
        <h2 class="text-white text-lg mb-4">调试信息</h2>
        <div class="text-white text-sm">
          <h3 class="font-bold mb-2">当前状态:</h3>
          <pre class="bg-gray-700 p-2 rounded text-xs overflow-auto max-h-96">{{ JSON.stringify(testLayersData, null, 2) }}</pre>
        </div>
        
        <div class="mt-4">
          <h3 class="text-white font-bold mb-2">UE 消息日志:</h3>
          <div class="bg-gray-700 p-2 rounded text-xs text-green-400 max-h-48 overflow-auto">
            <div v-for="(msg, index) in ueMessages" :key="index" class="mb-1">
              {{ new Date(msg.timestamp).toLocaleTimeString() }}: {{ JSON.stringify(msg.data) }}
            </div>
          </div>
          <button 
            @click="clearUeMessages" 
            class="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
          >
            清空日志
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import LayersTool from '@/components/LayersTool.vue'

// 测试数据
const testLayersData = ref({
  gasCompany: {
    label: '燃气企业',
    icon: 'industry',
    checked: false,
    children: [
      {
        id: 'pipeline-gas-1',
        label: '管道气',
        checked: false,
        ueData: [
          { command: 'AddPoint', args: { location: { x: 115.93331, y: 39.05121, z: 2.0 }, id: '12210', type: '1' } },
          { command: 'AddPoint', args: { location: { x: 115.92234, y: 39.05232, z: 2.0 }, id: '12211', type: '1' } },
        ],
        ueRemoveData: { command: 'RemovePoint', args: { type: '1' } },
      },
      {
        id: 'lng-gas-1',
        label: 'LNG气',
        checked: false,
        ueData: [
          { command: 'AddPoint', args: { location: { x: 115.94454, y: 39.05342, z: 2.0 }, id: '12212', type: '2' } },
        ],
        ueRemoveData: { command: 'RemovePoint', args: { type: '2' } },
      },
    ],
  },
  pipeline: {
    label: '管网设施',
    icon: 'pipeline',
    checked: false,
    children: [
      {
        id: 'high-pressure',
        label: '高压管道',
        checked: false,
        ueData: [
          { command: 'AddLine', args: { id: '13001', type: '1' } },
        ],
        ueRemoveData: { command: 'RemoveLine', args: { type: '1' } },
      },
    ],
  },
  hazardSource: {
    label: '重大危险源',
    icon: 'fallout',
    checked: false,
    ueData: [
      { command: 'AddHazard', args: { location: { x: 115.93331, y: 39.05121, z: 2.0 }, id: '14001', type: 'hazard' } },
    ],
    ueRemoveData: { command: 'RemoveHazard', args: { type: 'hazard' } },
  },
})

// UE 消息日志
const ueMessages = ref<Array<{ timestamp: number; data: any }>>([])

// 模拟 sendToUe 函数来捕获消息
const originalSendToUe = window.sendToUe || (() => {})

onMounted(() => {
  // 拦截 UE 消息
  window.sendToUe = (data: any) => {
    ueMessages.value.push({
      timestamp: Date.now(),
      data: data
    })
    console.log('UE Message:', data)
    // 调用原始函数（如果存在）
    originalSendToUe(data)
  }
})

const clearUeMessages = () => {
  ueMessages.value = []
}
</script>

<style scoped>
/* 测试页面样式 */
</style>
