<template>
  <div class="panel-container-half">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">气瓶充装监管</div>
      <div class="header-dropdown">
        <Select v-bind:model-value="selectedOpt">
          <SelectTrigger class="text-sm dropdown-btn" size="sm">
            <SelectValue placeholder="监管周期" />
          </SelectTrigger>
          <SelectContent class="text-[#99D5FF]">
            <SelectGroup>
              <!-- <SelectLabel>Fruits</SelectLabel> -->
              <SelectItem value="day">本周每日</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  // SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
const selectedOpt = ref<string>('day')
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1

let xAxis = ['07/28', '07/29', '07/30', '07/31', '08/01', '08/02', '08/03']
let yData = [120, 200, 150, 80, 70, 110, 130]

const option = {
  backgroundColor: 'transparent',
  grid: {
    left: '0%',
    right: '0%',
    top: '15%',
    bottom: '0%',
    containLabel: true,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    textStyle: {
      color: '#fff',
    },
    backgroundColor: 'rgba(42, 56, 77, 0.6)',
    borderWidth: 0,
  },
  xAxis: [
    {
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#fff',
          type: 'solid',
          opacity: 0.3,
        },
      },
      data: xAxis,
    },
    {
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      data: xAxis,
    },
  ],
  yAxis: {
    type: 'value',
    name: '单位：个',
    nameTextStyle: {
      color: 'rgba(255, 255, 255, 1)',
      fontSize: 12,
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.3)',
      },
    },
    axisLabel: {
      color: 'rgba(255, 255, 255, 1)',
      fontSize: 12,
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.1)',
      },
    },
  },
  series: [
    {
      type: 'bar',
      barWidth: 20,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#47EBEB99' },
            { offset: 1, color: '#47EBEB26' },
          ],
        },
        borderColor: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#99D5FF' },
            { offset: 1, color: '#99D5FF00' },
          ],
        },
        borderWidth: 1,
      },
      data: yData,
    },
    {
      name: 'icon',
      type: 'pictorialBar',
      emphasis: {
        disabled: true,
      },
      symbol: 'rect',
      symbolRotate: 45,
      symbolPosition: 'end',
      symbolSize: [7, 7],
      symbolOffset: [0, '-50%'],
      z: 10,
      itemStyle: {
        color: '#99D5FF',
      },
      tooltip: { show: false },
      data: yData,
    },
    {
      type: 'bar',
      xAxisIndex: 1,
      barWidth: 1,
      itemStyle: {
        color: 'transparent',
        borderColor: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#99D5FF' },
            { offset: 1, color: '#99D5FF00' },
          ],
        },
        borderWidth: 1,
        borderType: [1, 3],
      },
      tooltip: { show: false },
      data: yData,
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = option.xAxis[0].data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.dropdown-btn {
  border: 1px solid #99d5ff;
}

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
