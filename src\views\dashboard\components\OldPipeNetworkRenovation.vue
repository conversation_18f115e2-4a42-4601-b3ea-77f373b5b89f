<template>
  <div class="panel-container">
    <div class="panel-header">
      <div class="header-title">老旧管网改造</div>
    </div>
    <div class="p-4 panel-content">
      <div class="content-layout">
        <div class="left-section">
          <div ref="chartRef" class="donut-chart"></div>
        </div>
        <div class="right-section">
          <div class="progress-circle">
            <div class="progress-animation"></div>
            <div class="progress-value">100%</div>
          </div>
          <div class="area-info">
            <div class="area-item">
              <div class="area-icon"></div>
              <div class="area-text">计划改造里程</div>
              <div class="area-value">3.1km</div>
            </div>
            <div class="area-item">
              <div class="area-icon"></div>
              <div class="area-text">已改造里程数</div>
              <div class="area-value">3.1km</div>
            </div>
            <div class="area-item">
              <div class="area-icon"></div>
              <div class="area-text">改造完成率</div>
              <div class="area-value">100%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1

const option = {
  backgroundColor: 'transparent',
  title: {
    left: '39%', //?
    top: '38%',
    textAlign: 'center',
    text: '材质分析',
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    subtext: '116.2',
    subtextStyle: {
      color: '#fff',
      fontSize: 24,
    },
  },
  legend: {
    show: true,
    orient: 'vertical',
    right: '10%',
    bottom: '10%',
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    itemWidth: 12,
    itemHeight: 8,
    icon: 'rect',
  },
  series: [
    {
      type: 'pie',
      radius: ['36.1%', '74.1%'],
      center: ['40%', '50%'],
      silent: true,
      data: [{ value: 1, itemStyle: { color: 'rgba(153, 213, 255, 0.15)' } }],
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
    },
    {
      type: 'pie',
      radius: ['41.7%', '68.5%'],
      center: ['40%', '50%'],
      data: [
        { value: 40, name: '钢管', itemStyle: { color: '#687af3' } },
        { value: 25, name: '铸铁管', itemStyle: { color: '#99d5ff' } },
        { value: 15, name: '其它', itemStyle: { color: '#E19760' } },
        { value: 20, name: '非金属管', itemStyle: { color: '#a3f58f' } },
      ],
      label: {
        show: true,
        color: '#fff',
        formatter: `{percent|{d}%}\n{value|{c}}个`,
        rich: {
          percent: {
            fontSize: 20,
            color: '#fff',
          },
          value: {
            fontSize: 12,
            color: '#fff',
          },
        },
      },
      labelLine: {
        show: true,
      },
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startHighlightAnimation()
}

// 开始高亮轮播动画
const startHighlightAnimation = () => {
  const dataCount = option.series[1].data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 1, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 1, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.content-layout {
  display: flex;
  height: 100%;
  gap: 20px;
}

.left-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* padding: 16px; */
}

.donut-chart {
  width: 468px;
  height: 216px;
  position: relative;
}

.right-section {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.progress-circle {
  width: 160px;
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 160px;
  height: 160px;
  background: url('@/assets/old-pipe-network-renovation/percent-bg.png') no-repeat center;
  background-size: 100% 100%;
  border-radius: 50%;
  will-change: transform;
  animation: spin 5s linear infinite;
}

.progress-value {
  font-family: NotoSansSC;
  font-size: 32px;
  font-weight: normal;
  line-height: 40px;
  letter-spacing: normal;
  background: linear-gradient(180deg, rgba(149, 243, 255, 0) 0%, rgba(149, 243, 255, 0) 52%, #95f3ff 100%), #ffffff;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* text-fill-color: transparent; */
}

.area-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.area-item {
  /* width: 216px; */
  width: 230px;
  height: 48px;
  background: url('@/assets/old-pipe-network-renovation/statistics-bg.png') no-repeat center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  gap: 8px;
}

.area-icon {
  width: 28px;
  height: 28px;
  margin: 4px 0 0 12px;
  background: url('@/assets/old-pipe-network-renovation/statistics-icon.png') no-repeat center;
  background-size: 100% 100%;
}

.area-text {
  height: 28px;
  padding: 0 4px;
  color: #fff;
  font-size: 16px;
  line-height: 28px;
  border-radius: 0px 0px 4px 4px;
  background: linear-gradient(180deg, rgba(149, 243, 255, 0) 57%, #95f3ff 100%);
}

.area-value {
  flex-grow: 1;
  padding-right: 12px;
  color: white;
  font-size: 20px;
  line-height: 28px;
  font-weight: 500;
  text-align: right;
}
</style>
