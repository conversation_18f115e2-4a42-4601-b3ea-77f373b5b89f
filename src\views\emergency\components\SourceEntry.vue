<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

const emit = defineEmits<{
  (e: 'click', value: any): void
}>()

const handleShowDialog = (record: any) => {
  console.log('查看记录:', record)
  emit('click', record)
}

const sourceData = ref([
  {
    name: '防火毯',
    time: '2025-07-20 11:38:29',
    personInCharge: '王XX',
    address: 'xx镇xx村xxxx',
    nature: '物资仓库/消防设施/医院',
    phone: '12345567894',
  },
  {
    name: '干粉灭火器',
    time: '2025-07-18 10:10:54',
    personInCharge: '李XX',
    address: 'xx区xx路xx号',
    nature: '应急队伍',
    phone: '139xxxxxxxx',
  },
  {
    name: '干粉灭火器',
    time: '2025-07-18 10:10:54',
    personInCharge: '市政维护',
    address: 'xx街道xx交叉口',
    nature: '消防设施',
    phone: 'N/A',
  },
  {
    name: '防汛沙袋',
    time: '2025-06-27 14:34:21',
    personInCharge: '院办',
    address: 'xx大道xx号',
    nature: '医院',
    phone: '0755-xxxxxxx',
  },
  {
    name: '防汛沙袋',
    time: '2025-06-27 14:34:21',
    personInCharge: '区应急办',
    address: 'xx公园',
    nature: '避难场所',
    phone: 'N/A',
  },
  {
    name: '铁锹',
    time: '2025-06-27 14:34:21',
    personInCharge: '王XX',
    address: 'xx镇xx村xxxx',
    nature: '物资仓库/消防设施/医院',
    phone: '12345567894',
  },
  {
    name: '消防面罩',
    time: '2025-05-20 11:38:29',
    personInCharge: '李XX',
    address: 'xx区xx路xx号',
    nature: '应急队伍',
    phone: '139xxxxxxxx',
  },
  {
    name: '防火毯',
    time: '2025-05-20 11:38:29',
    personInCharge: '王XX',
    address: 'xx镇xx村xxxx',
    nature: '物资仓库/消防设施/医院',
    phone: '12345567894',
  },
  {
    name: '干粉灭火器',
    time: '2025-05-18 10:10:54',
    personInCharge: '李XX',
    address: 'xx区xx路xx号',
    nature: '应急队伍',
    phone: '139xxxxxxxx',
  },
])

const scrollList = computed(() => [...sourceData.value, ...sourceData.value])
const tableContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const rowHeight = 40 // 每行高度为40px
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight

    if (scrollTop.value >= sourceData.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
      }, 50)
    }
  }, 3000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  startScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.addEventListener('mouseenter', stopScrolling)
    container.addEventListener('mouseleave', startScrolling)
  }
})

onUnmounted(() => {
  stopScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>

<template>
  <div class="panel-container-hfHgt-hfWdt">
    <div class="panel-header">最新资源入库</div>
    <div class="p-4 panel-content">
      <Table class="w-full table-fixed">
        <colgroup>
          <col style="width: 30%" />
          <col style="width: auto" />
          <col style="width: 20%" />
        </colgroup>
        <TableHeader>
          <TableRow class="border-[#99D5FF]/30 hover:bg-transparent">
            <TableHead class="font-bold text-white">资源名称</TableHead>
            <TableHead class="font-bold text-white">入库时间</TableHead>
            <TableHead class="font-bold text-white">操作</TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div class="overflow-hidden" :style="{ height: `${8 * rowHeight}px` }" ref="tableContainerRef">
        <Table class="w-full table-fixed">
          <colgroup>
            <col style="width: 30%" />
            <col style="width: auto" />
            <col style="width: 20%" />
          </colgroup>
          <TableBody
            :style="{
              transform: `translateY(-${scrollTop}px)`,
              transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
            }"
          >
            <TableRow
              v-for="(item, index) in scrollList"
              :key="index"
              class="border-[#99D5FF]/30 hover:bg-[#99D5FF]/30"
              :style="{ height: `${rowHeight}px` }"
            >
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.name }}</TableCell>
              <TableCell>{{ item.time }}</TableCell>
              <TableCell class="text-[#99D5FF] cursor-pointer" @click="handleShowDialog(item)">查看</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import '@/styles/index.css';
</style>
