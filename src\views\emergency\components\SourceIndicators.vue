<template>
  <div class="panel-container-hfHgt-hfWdt">
    <div class="panel-header">应急资源</div>
    <div class="flex flex-wrap justify-between gap-4 p-4">
      <div v-for="(item, idx) in indList" :key="idx" class="flex items-center justify-center flex-none gap-3 indicator">
        <div class="indicator-left">
          <span class="w-7 h-7 indicator-icon"></span>
        </div>
        <div>
          <span class="text-[#99D5FF] text-sm">{{ item.name }}</span>
          <p>
            <span class="text-xl font-bold text-white">{{ item.value }}</span>
            <span class="text-sm text-white">{{ item.unit }}</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

const indList = ref([
  { name: '应急监控', value: 57152, unit: '个', id: 0 },
  { name: '应急队伍', value: 2, unit: '支', id: 1 },
  { name: '物资仓库', value: 4, unit: '个', id: 2 },
  { name: '抢修车辆', value: 116.2, unit: '辆', id: 3 },
  { name: '医院', value: 9, unit: '个', id: 4 },
  { name: '消防设施', value: 10, unit: '个', id: 5 },
])
</script>
<style scoped>
@import '@/styles/index.css';

.indicator {
  width: 216px;
  height: 115px;
  background: url('@/assets/emergency/indicator-bg.png') no-repeat center;
  background-size: cover;
}
.indicator-left {
  width: 56px;
  height: 48px;
  display: flex;
  justify-content: center;
  background: url('@/assets/emergency/ind-icon-bg.png') no-repeat center bottom;
  background-size: 56px 28px;
}
.indicator-icon {
  display: inline-block;
  width: 28px;
  height: 28px;
  background: url('@/assets/emergency/ind-1-1.png') no-repeat center;
  background-size: contain;
}
</style>
