<template>
  <div class="dashboard-layout">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <div class="flex gap-6">
        <div class="flex flex-col gap-6">
          <SourceIndicators />
          <SourceEntry @click="onShowSourceEntryDialog" />
        </div>
        <div class="flex flex-col gap-6">
          <OnlineRate />
          <OnlineVideo @click="onShowOnlineVideoDialog" />
          <PipelineLeakTable @click="onShowPipelineLeakDialog" />
        </div>
      </div>
    </div>

    <!-- 中央地图区域 -->
    <div class="center-panel-left">
      <LayersTool v-model="layersData" />
    </div>
    <div class="center-panel-right">
      <MapTool @click="onShowLeakModelDialog" />
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <div class="flex gap-6">
        <PipelineLeakLine />
        <PlanPie />
      </div>
      <div class="flex gap-6">
        <DrillsTop />
        <AccidentTable />
      </div>
    </div>
    <!-- 资源录入 -->
    <SourceEntryDialog :open="showSourceEntryDialog" :data="sourceEntryInfo" @close="showSourceEntryDialog = false" />
    <!-- 泄漏预演 -->
    <LeakModelDialog :open="showLeakModelDialog" @close="showLeakModelDialog = false" />
    <!-- 管网泄漏 -->
    <PipelineLeakDialog
      :open="showPipelineLeakDialog"
      :data="pipelineLeakInfo"
      @close="showPipelineLeakDialog = false"
    />
    <!-- 在线视频 -->
    <VideoDialog :open="showVideoDialog" @close="showVideoDialog = false" :data="videoInfo" />

    <!-- UE弹窗 -->
    <EmergencyResourceDialog :open="resourceInfoOpen" @close="resourceInfoOpen = false" />
    <EmergencyEventsDialog :open="eventInfoOpen" @close="eventInfoOpen = false" />
    <StationDialog :open="stationInfoOpen" @close="stationInfoOpen = false" />
    <PipelineDialog :open="pipelineInfoOpen" @close="pipelineInfoOpen = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import SourceIndicators from './components/SourceIndicators.vue'
import SourceEntry from './components/SourceEntry.vue'
import OnlineRate from './components/OnlineRate.vue'
import OnlineVideo from './components/OnlineVideo.vue'
import PipelineLeakTable from './components/PipelineLeakTable.vue'
import PipelineLeakLine from './components/PipelineLeakLine.vue'
import PlanPie from './components/PlanPie.vue'
import DrillsTop from './components/DrillsTop.vue'
import AccidentTable from './components/AccidentTable.vue'
import MapTool from './components/MapTool.vue'
import LeakModelDialog from './LeakModelDialog.vue'
import SourceEntryDialog from './SourceEntryDialog.vue'
import PipelineLeakDialog from './PipelineLeakDialog.vue'
import VideoDialog from './VideoDialog.vue'
import LayersTool from '@/components/LayersTool.vue'
import EmergencyResourceDialog from '@/components/ue-dialog/emergencyResourceDialog.vue'
import EmergencyEventsDialog from '@/components/ue-dialog/emergencyEventsDialog.vue'
import StationDialog from '@/components/ue-dialog/StationDialog.vue'
import PipelineDialog from '@/components/ue-dialog/PipelineDialog.vue'
import { sendToUe } from '@/utils/ue'
import { layerData } from './layerData'

const showLeakModelDialog = ref<boolean>(false)
const showSourceEntryDialog = ref<boolean>(false)
const showPipelineLeakDialog = ref<boolean>(false)
const showVideoDialog = ref<boolean>(false)

const sourceEntryInfo = ref<any>({})
const pipelineLeakInfo = ref<any>({})
const videoInfo = ref<any>({
  id: '1',
  title: '--',
  address: '--',
  url: '',
})

const resourceInfoOpen = ref(false)
// const industryInfoData: Ref<IndustryInfo | null> = ref(null)
const stationInfoOpen = ref(false)
// const stationInfoData: Ref<StationInfo | null> = ref(null)
const pipelineInfoOpen = ref(false)
// const pipelineInfoData: Ref<PipelineInfo | null> = ref(null)
const eventInfoOpen = ref(false)
// const areaInfoData: Ref<AreaInfo | null> = ref(null)

// 图层数据
const layersData = ref(layerData)

const onShowLeakModelDialog = (value: boolean) => {
  console.log('Dialog visibility:', value)
  // 可以在这里处理弹窗显示逻辑
  showLeakModelDialog.value = true
}
const onShowSourceEntryDialog = (record: any) => {
  sourceEntryInfo.value = record
  showSourceEntryDialog.value = true
  console.log('Source entry record:', record)
}
const onShowPipelineLeakDialog = (record: any) => {
  console.log('Pipeline leak record:', record)
  pipelineLeakInfo.value = record
  showPipelineLeakDialog.value = true
}
const onShowOnlineVideoDialog = (video: any) => {
  console.log('Online video clicked:', video)
  videoInfo.value = video
  // 可以在这里处理弹窗显示逻辑
  showVideoDialog.value = true
}

// UE交互处理
const handleUeClickPoint = async (data: { id: string; type?: string }) => {
  const { id } = data
  const idNumber = parseInt(id.replace(/\D/g, ''), 10)

  if (idNumber >= 12210 && idNumber <= 12223) {
    resourceInfoOpen.value = true
  } else if (idNumber >= 12271 && idNumber <= 12284) {
    stationInfoOpen.value = true
  } else {
    eventInfoOpen.value = true
  }
}

const handleUeClickLine = async () => {
  // pipelineInfoData.value = await fetchPipelineInfo(data.id)
  pipelineInfoOpen.value = true
}

onMounted(() => {
  // 将处理函数挂载到 window.ue.interface
  if (window.ue && window.ue.interface) {
    window.ue.interface.clickpoint = handleUeClickPoint
    window.ue.interface.clickline = handleUeClickLine
  } else {
    console.error('UE interface is not available.')
    // for debug
    window.ue = {
      interface: {
        broadcast: () => {},
      },
    }
    window.ue.interface.clickpoint = handleUeClickPoint
    window.ue.interface.clickline = handleUeClickLine
  }
})

onUnmounted(() => {
  // 清理
  if (window.ue && window.ue.interface) {
    delete window.ue.interface.clickpoint
    delete window.ue.interface.clickline
  }
  // 清除所有图层
  sendToUe({ command: 'RemoveAll', args: {} })
})
</script>

<style scoped>
.dashboard-layout {
  position: absolute;
  top: 121px;
  left: 24px;
  right: 24px;
  height: calc(100% - 121px);
  overflow: hidden;
}

/* .dashboard-content {
  display: grid;
  grid-template-columns: 350px 1fr 350px;
  gap: 24px;
  padding: 24px;
  padding-top: 120px;
  width: 100%;
  height: 100%;
  margin: 0 auto;
} */
.left-panel,
.right-panel {
  position: absolute;
  top: 0;
  width: 984px;
}
.left-panel {
  left: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.right-panel {
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.center-panel-left {
  position: absolute;
  top: 0;
  left: 1008px;
  z-index: 10;
}

.center-panel-right {
  position: absolute;
  top: 0;
  right: 1008px;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    padding: 20px;
    padding-top: 120px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 280px 1fr 280px;
    gap: 16px;
    padding: 16px;
    padding-top: 120px;
  }
}
</style>
