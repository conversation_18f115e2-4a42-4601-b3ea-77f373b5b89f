import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'index',
      redirect: '/gas',
    },
    {
      path: '/gas',
      name: 'gas',
      meta: {
        title: '智慧燃气',
      },
      component: () => import('@/views/dashboard/index.vue'),
    },
    {
      path: '/runMon',
      name: 'runMon',
      meta: {
        title: '运行监测',
      },
      component: () => import('@/views/runMonitor/index.vue'),
    },
    {
      path: '/emergency',
      name: 'emergency',
      meta: {
        title: '应急管理',
      },
      component: () => import('@/views/emergency/index.vue'),
    },
    {
      path: '/hazard',
      name: 'hazard',
      meta: {
        title: '重大危险源',
      },
      component: () => import('@/views/hazard/index.vue'),
    },
    {
      path: '/test',
      name: 'test',
      meta: {
        title: 'LayersTool 测试',
      },
      component: () => import('@/test/LayersToolTest.vue'),
    },
  ],
})

export default router
