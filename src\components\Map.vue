<template>
  <div class="map-container">
    <div id="contain" style="background: #0d1429"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'

const props = defineProps({
  center: {
    type: Array,
    default: () => [115.914607, 39.054086],
  },
  zoom: {
    type: Number,
    default: 12,
  },
  viewMode: {
    type: String,
    default: '3D',
  },
  pitch: {
    type: Number,
    default: 44.9,
  },
  clickable: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['map-click', 'map-ready'])

// 常量定义
const defaultView = {
  pos: props.center,
  pitch: props.pitch,
  rotation: 0,
  zoom: props.zoom,
}

// 响应式状态
const map = ref(null)
const loca = ref(null)
const AMap = ref(null)
const autoTourTimer = ref(null)
const markerGeo = ref({})
const marker = ref(null)
const heatmapLayer = ref(null)
const outLayer = ref(null)

// 工具函数
const getRandom = (min, max) => {
  return Math.random() * (max - min) + min
}

// 初始化地图
const initMap = async () => {
  try {
    const AMapInstance = await AMapLoader.load({
      key: '8a6865ecdde60542806eb4dd4da77aed',
      version: '2.0',
      plugins: [],
      Loca: { version: '2.0.0' },
    })

    AMap.value = AMapInstance
    map.value = new AMapInstance.Map('contain', {
      zoom: props.zoom,
      viewMode: props.viewMode,
      pitch: props.pitch,
      rotation: 0,
      center: props.center,
      mapStyle: 'amap://styles/23bd04fd6d3479b194f59d813bfb4d4d',
      skyColor: '#081245',
      willReadFrequently: true,
    })

    loca.value = new Loca.Container({ map: map.value, willReadFrequently: true })

    // 设置全局变量便于调试
    window.mymap = map.value
    window.myloca = loca.value

    // 创建边界线图层

    // loca.value.add(outLayer.value)
    loca.value.pointLight.intensity = 0
    loca.value.ambLight.intensity = 1

    map.value.on('complete', () => {
      loca.value.animate.start()
      emit('map-ready')
    })

    if (props.clickable) {
      map.value.on('click', e => {
        // console.log(map.value.getCenter(), map.value.getZoom(),
        //            map.value.getRotation(), map.value.getPitch())
        // 清除上一个标记
        if (marker.value) {
          map.value.remove(marker.value)
        }
        // 创建新的标记
        marker.value = new AMap.value.Marker({
          position: e.lnglat,
        })
        map.value.add(marker.value)
        // 发送事件
        emit('map-click', e.lnglat)
      })
    }
  } catch (error) {
    console.error('地图加载失败:', error)
  }
}

// 重置视图
const resetView = () => {
  goPositionAni(defaultView.pos, defaultView.pitch, defaultView.rotation, defaultView.zoom)
}

// 显示热力图
const showHeatMap = heatData => {
  destroyHeatMap() // 先移除已有的

  if (!heatData || heatData.length === 0) {
    return
  }

  const heatGeo = new Loca.GeoJSONSource({
    data: {
      type: 'FeatureCollection',
      features: heatData.map(item => ({
        type: 'Feature',
        properties: { num: item.num },
        geometry: {
          type: 'Point',
          coordinates: item.lnglat,
        },
      })),
    },
  })

  heatmapLayer.value = new Loca.HeatMapLayer({
    zIndex: 10,
    opacity: 1,
    visible: true,
    zooms: [2, 22],
  })

  heatmapLayer.value.setSource(heatGeo, {
    radius: 30, // 半径
    unit: 'meter',
    height: 100, // 高度
    gradient: {
      0.1: '#2A85B8',
      0.2: '#16B0A9',
      0.3: '#29CF6F',
      0.4: '#5CE182',
      0.5: '#7DF675',
      0.6: '#FFF100',
      0.7: '#FAA53F',
      1: '#D04343',
    },
    value: (index, feature) => feature.properties.num,
    heightBezier: [0, 0.53, 0.37, 0.98],
  })

  loca.value.add(heatmapLayer.value)
}

// 销毁热力图
const destroyHeatMap = () => {
  if (heatmapLayer.value) {
    loca.value.remove(heatmapLayer.value)
    heatmapLayer.value = null
  }
}

// 清理地图数据
const clearLoca = () => {
  loca.value.clear()
}

const addMarker = position => {
  if (marker.value) {
    map.value.remove(marker.value)
  }
  marker.value = new AMap.value.Marker({
    position: [position.lng, position.lat],
  })
  map.value.add(marker.value)
}

const destroyMarker = () => {
  if (marker.value) {
    map.value.remove(marker.value)
    marker.value = null
  }
}

const polygon = ref(null)

// 绘制多边形
const drawPolygon = path => {
  destroyPolygon()
  polygon.value = new AMap.value.Polygon({
    path: path,
    strokeColor: '#00FF00',
    strokeWeight: 2,
    strokeOpacity: 0.8,
    fillColor: '#00FF00',
    fillOpacity: 0.3,
    zIndex: 50,
  })
  map.value.add(polygon.value)
  map.value.setFitView([polygon.value])
}

// 销毁多边形
const destroyPolygon = () => {
  if (polygon.value) {
    map.value.remove(polygon.value)
    polygon.value = null
  }
}

// 生命周期钩子
onMounted(initMap)

defineExpose({
  showHeatMap,
  destroyHeatMap,
  addMarker,
  destroyMarker,
  drawPolygon,
  destroyPolygon,
})

onBeforeUnmount(() => {
  if (loca.value) loca.value.destroy()
  if (map.value) map.value.destroy()
  if (autoTourTimer.value) clearTimeout(autoTourTimer.value)
})
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #0d1429;
}

#contain {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  display: flex;
  gap: 10px;
  z-index: 100;
}

.control-panel button {
  padding: 8px 15px;
  background: rgba(30, 96, 174, 0.7);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.control-panel button:hover {
  background: rgba(45, 206, 252, 0.9);
}
</style>
