## 燃气大屏（新版）

### 快速开始

```bash
pnpm install
pnpm dev
```

### 生产打包

```bash
pnpm build
pnpm preview
```

开发所处环境：

- node v22.14.0
- pnpm v10.7.0

### 目录结构

```
├── public
│   ├── favicon.ico
│   └── index.html
├── src
│   ├── assets
│   │   ├── footer
│   │   │   ├── scroll-icon.png
│   │   │   └── scroll-icon-active.png
│   │   ├── header
│   │   │   ├── logo.png
│   │   │   └── <EMAIL>
│   │   └── images
│   │       ├── bg.png
│   │       ├── <EMAIL>
│   │       ├── <EMAIL>
│   │       ├── <EMAIL>
│   │       └── <EMAIL>
│   ├── components
│   │   ├── ui
│   │   │   ├── button
│   │   │   │   ├── index.ts
│   │   │   │   └── index.vue
│   │   │   ├── carousel
│   │   │   │   ├── index.ts
│   │   │   │   ├── interface.ts
│   │   │   │   ├── useCarousel.ts
│   │   │   │   └── index.vue
│   │   │   └── index.ts
│   │   ├── index.ts
│   │   └── map.vue
│   ├── layouts
│   │   ├── components
│   │   │   ├── footer.vue
│   │   │   └── header.vue
│   │   ├── index.vue
│   │   └── useLayout.ts
│   ├── locales
│   │   ├── en.json
│   │   ├── index.ts
│   │   └── zh.json
│   ├── main.ts
│   ├── router
│   │   ├── index.ts
│   │   └── routes.ts
│   ├── store
│   │   ├── index.ts
│   │   └── modules
│   │       ├── app.ts
│   │       ├── index.ts
│   │       └── user.ts
│   ├── styles
│   │   ├── index.scss
│   │   └── variables.scss
│   ├── utils
│   │   ├── index.ts
│   │   └── useWindowResize.ts
│   └── views
│       ├── home.vue
│       ├── index.vue
│       └── not-found.vue
├── .env
├── .env.development
├── .env.production
├── .gitignore
├── .npmrc
├── .prettierrc
├── .prettierignore
├── .eslintrc.js
├── .eslintignore
├── pnpm-lock.yaml
├── pnpm-workspace.yaml
├── tsconfig.json
├── tsconfig.node.json
├── vite.config.ts
└── tailwind.config.js
```
