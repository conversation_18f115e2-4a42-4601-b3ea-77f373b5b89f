.panel-container {
  width: 984px;
  height: 296px;
  color: #fff;
  box-sizing: border-box;
  background: rgba(42, 56, 77, 0.6) url('@/assets/panel/section-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
  backdrop-filter: blur(8px);
  border-radius: 8px;
}
.panel-container-hfHgt-hfWdt {
  width: 480px;
  height: 456px;
  color: #fff;
  box-sizing: border-box;
  background: rgba(42, 56, 77, 0.6) url('@/assets/panel/section-hfH-hfW.png') no-repeat 0 0;
  background-size: 100% 100%;
  backdrop-filter: blur(8px);
  border-radius: 8px;
}
.panel-container-half {
  width: 480px;
  height: 296px;
  color: #fff;
  box-sizing: border-box;
  background: rgba(42, 56, 77, 0.6) url('@/assets/panel/section-half.png') no-repeat 0 0;
  background-size: 100% 100%;
  backdrop-filter: blur(8px);
  border-radius: 8px;
}
.panel-container-col {
  width: 480px;
  height: 616px;
  color: #fff;
  box-sizing: border-box;
  background: rgba(42, 56, 77, 0.6) url('@/assets/panel/section-bg-480x616.png') no-repeat 0 0;
  background-size: 100% 100%;
  backdrop-filter: blur(8px);
  border-radius: 8px;
}
.panel-header {
  padding: 0 16px 0 52px;
  height: 44px;
  line-height: 44px;
  text-align: left;
  font-family: MStiffHei PRC;
  font-size: 18px;
}
.panel-content {
  width: 100%;
  height: calc(100% - 44px);
}
