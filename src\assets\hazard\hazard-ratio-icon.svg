<svg width="123" height="122" viewBox="0 0 123 122" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M61 121C27.8629 121 1 94.1371 1 61C1 27.8629 27.8629 1 61 1C94.1371 1 121 27.8629 121 61C121 94.1371 94.1371 121 61 121ZM61 112C32.8335 112 10 89.1665 10 61C10 32.8335 32.8335 10 61 10C89.1665 10 112 32.8335 112 61C112 89.1665 89.1665 112 61 112Z" fill="#8CFFFF" fill-opacity="0.15"/>
<circle cx="61" cy="61" r="60" stroke="#8CFFFF" stroke-opacity="0.3"/>
<path d="M121 61C121 68.8793 119.448 76.6815 116.433 83.961C113.418 91.2405 108.998 97.8549 103.427 103.426" stroke="#8CFFFF" stroke-width="4" stroke-linecap="round">
  <animateTransform attributeName="transform" type="rotate" from="0 61 61" to="360 61 61" dur="2s" repeatCount="indefinite"/>
</path>
<circle cx="61" cy="61" r="51" stroke="#8CFFFF" stroke-opacity="0.3"/>
<path d="M10.2307 60.9967C10.2307 54.3296 11.5439 47.7277 14.0953 41.5681C16.6467 35.4085 20.3863 29.8118 25.1007 25.0974" stroke="#8CFFFF" stroke-width="4" stroke-linecap="round">
  <animateTransform attributeName="transform" type="rotate" from="0 61 61" to="-360 61 61" dur="2s" repeatCount="indefinite"/>
</path>
<circle cx="61" cy="61" r="39" stroke="#8CFFFF" stroke-opacity="0.3" stroke-width="10"/>
<g clip-path="url(#paint0_angular_2_18168_clip_path)" data-figma-skip-parse="true">
  <g transform="matrix(0 -0.0385002 0.0385002 0 61 61)">
    <foreignObject x="-1272.72" y="-1272.72" width="2545.44" height="2545.44">
      <div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(140, 255, 255, 0.0211) 0deg,rgba(140, 255, 255, 1) 312.997deg,rgba(140, 255, 255, 0) 353.257deg,rgba(140, 255, 255, 0.0211) 360deg);height:100%;width:100%;opacity:1">
      </div>
    </foreignObject>
  </g>
  <animateTransform attributeName="transform" type="rotate" from="0 61 61" to="360 61 61" dur="2s" repeatCount="indefinite"/>
</g>
<path d="M61 17C58.2386 17 56 19.2386 56 22C56 24.7614 58.2386 27 61 27L61 17ZM33.4934 41.0153C35.1166 38.7813 34.6213 35.6544 32.3873 34.0313C30.1532 32.4082 27.0264 32.9034 25.4033 35.1374L33.4934 41.0153ZM61 27C68.637 27 76.0517 29.5712 82.0492 34.2992L88.2401 26.446C80.4786 20.3274 70.8832 17 61 17L61 27ZM82.0492 34.2992C88.0467 39.0273 92.2777 45.6368 94.0605 53.0628L103.784 50.7284C101.477 41.1182 96.0016 32.5647 88.2401 26.446L82.0492 34.2992ZM94.0605 53.0628C95.8434 60.4889 95.0742 68.2989 91.8768 75.2344L100.958 79.421C105.096 70.4456 106.091 60.3385 103.784 50.7284L94.0605 53.0628ZM91.8768 75.2344C88.6795 82.1699 83.2403 87.827 76.4357 91.2942L80.9756 100.204C89.7816 95.7173 96.8206 88.3964 100.958 79.421L91.8768 75.2344ZM76.4357 91.2942C69.631 94.7613 61.8572 95.8366 54.3669 94.3467L52.416 104.155C62.1094 106.083 72.1696 104.691 80.9756 100.204L76.4357 91.2942ZM54.3669 94.3467C46.8766 92.8567 40.1061 88.8884 35.1462 83.0812L27.5422 89.5757C33.9608 97.0909 42.7227 102.226 52.416 104.155L54.3669 94.3467ZM35.1462 83.0812C30.1863 77.2739 27.3261 69.966 27.0262 62.3348L17.0339 62.7274C17.422 72.603 21.1235 82.0604 27.5422 89.5757L35.1462 83.0812ZM27.0262 62.3348C26.7264 54.7037 29.0045 47.1938 33.4934 41.0153L25.4033 35.1374C19.594 43.1331 16.6459 52.8518 17.0339 62.7274L27.0262 62.3348Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.27999997138977051,&#34;g&#34;:0.92000019550323486,&#34;b&#34;:0.92000001668930054,&#34;a&#34;:1.0},&#34;position&#34;:0.86943733692169189},{&#34;color&#34;:{&#34;r&#34;:0.27999997138977051,&#34;g&#34;:0.92000019550323486,&#34;b&#34;:0.92000001668930054,&#34;a&#34;:0.0},&#34;position&#34;:0.98126989603042603}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:2.6806276382657090e-12,&#34;m01&#34;:77.000328063964844,&#34;m02&#34;:22.499834060668945,&#34;m10&#34;:-77.000328063964844,&#34;m11&#34;:6.7314387570960310e-12,&#34;m12&#34;:99.500160217285156},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"></path>
<defs>
<clipPath id="paint0_angular_2_18168_clip_path"><path d="M61 17C58.2386 17 56 19.2386 56 22C56 24.7614 58.2386 27 61 27L61 17ZM33.4934 41.0153C35.1166 38.7813 34.6213 35.6544 32.3873 34.0313C30.1532 32.4082 27.0264 32.9034 25.4033 35.1374L33.4934 41.0153ZM61 27C68.637 27 76.0517 29.5712 82.0492 34.2992L88.2401 26.446C80.4786 20.3274 70.8832 17 61 17L61 27ZM82.0492 34.2992C88.0467 39.0273 92.2777 45.6368 94.0605 53.0628L103.784 50.7284C101.477 41.1182 96.0016 32.5647 88.2401 26.446L82.0492 34.2992ZM94.0605 53.0628C95.8434 60.4889 95.0742 68.2989 91.8768 75.2344L100.958 79.421C105.096 70.4456 106.091 60.3385 103.784 50.7284L94.0605 53.0628ZM91.8768 75.2344C88.6795 82.1699 83.2403 87.827 76.4357 91.2942L80.9756 100.204C89.7816 95.7173 96.8206 88.3964 100.958 79.421L91.8768 75.2344ZM76.4357 91.2942C69.631 94.7613 61.8572 95.8366 54.3669 94.3467L52.416 104.155C62.1094 106.083 72.1696 104.691 80.9756 100.204L76.4357 91.2942ZM54.3669 94.3467C46.8766 92.8567 40.1061 88.8884 35.1462 83.0812L27.5422 89.5757C33.9608 97.0909 42.7227 102.226 52.416 104.155L54.3669 94.3467ZM35.1462 83.0812C30.1863 77.2739 27.3261 69.966 27.0262 62.3348L17.0339 62.7274C17.422 72.603 21.1235 82.0604 27.5422 89.5757L35.1462 83.0812ZM27.0262 62.3348C26.7264 54.7037 29.0045 47.1938 33.4934 41.0153L25.4033 35.1374C19.594 43.1331 16.6459 52.8518 17.0339 62.7274L27.0262 62.3348Z"/></clipPath></defs>
</svg>
