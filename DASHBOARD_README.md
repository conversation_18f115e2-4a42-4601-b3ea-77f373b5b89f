# 燃气大屏项目 - 城市生命线燃气安全感知运营平台

## 项目概述

本项目是一个基于Vue 3 + TypeScript + ECharts的燃气安全监控大屏系统，用于展示城市燃气安全相关的各项数据和指标。

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: shadcn-vue
- **样式框架**: Tailwind CSS
- **图表库**: ECharts 5
- **图标库**: Lucide Vue Next
- **构建工具**: Vite
- **包管理器**: pnpm

## 项目结构

```
src/
├── components/          # 公共组件
│   ├── ui/             # shadcn-vue UI组件
│   └── Map.vue         # 地图组件
├── layouts/            # 布局组件
│   ├── components/
│   │   ├── Header.vue  # 头部导航
│   │   └── Footer.vue  # 底部组件
│   └── index.vue       # 主布局
├── views/
│   └── dashboard/      # 仪表板页面
│       ├── components/ # 页面组件
│       │   ├── IndustryOverview.vue           # 行业概况
│       │   ├── HouseholdInspection.vue        # 入户巡查
│       │   ├── SupervisionInspection.vue      # 监督检查
│       │   ├── RiskHazardMonitoring.vue       # 风险隐患监管
│       │   ├── OldPipeNetworkRenovation.vue   # 老旧管网改造
│       │   ├── BottledGasStatus.vue           # 瓶装气状态
│       │   ├── CylinderFillingMonitoring.vue  # 气瓶冲装监管
│       │   ├── GasLeakAlerts.vue              # 燃气泄漏警报
│       │   └── charts/                        # 图表组件
│       └── index.vue   # 仪表板主页面
└── lib/
    └── utils.ts        # 工具函数
```

## 功能模块

### 1. 头部导航 (Header.vue)

- 平台标题：城市生命线燃气安全感知运营平台
- 导航菜单：包含12个功能模块的导航标签
- 响应式设计，支持不同屏幕尺寸

### 2. 左侧面板

#### 行业概况 (IndustryOverview.vue)

- 显示管道燃气企业的统计数据
- 4x4网格布局展示16个数据指标
- 包含企业数量、用户数量、管道长度等信息

#### 入户巡查 (HouseholdInspection.vue)

- 5个圆形进度指示器
- 显示巡检计划、完成情况、完成率等指标
- 支持统计周期选择

#### 监督检查 (SupervisionInspection.vue)

- ECharts柱状图展示各镇监督检查数据
- 显示全部和办结两个维度的数据
- 支持检查任务筛选

### 3. 右侧面板

#### 风险隐患监管 (RiskHazardMonitoring.vue)

- 4个风险等级指标（一级到四级风险）
- 3个状态指标（现有隐患、已解决、处置率）
- ECharts柱状图展示各镇风险和隐患数据

#### 老旧管网改造 (OldPipeNetworkRenovation.vue)

- ECharts环形图展示材质分析数据
- 圆形进度指示器显示改造进度
- 总面积信息展示

#### 瓶装气状态 (BottledGasStatus.vue)

- 3个状态指标（在用、停用、废弃）
- ECharts环形图展示状态分布
- 图标化状态显示

#### 气瓶冲装监管 (CylinderFillingMonitoring.vue)

- ECharts柱状图展示每日冲装数据
- 支持时间周期选择
- 实时数据监控

### 4. 底部警报 (GasLeakAlerts.vue)

- 燃气泄漏警报信息
- 3个警报卡片展示
- 包含查看和处理按钮
- 脉冲动画效果

## 设计特点

### 视觉设计

- **深色主题**: 采用深蓝色渐变背景
- **玻璃拟态效果**: 半透明面板配合模糊效果
- **现代化UI**: 圆角设计、阴影效果、渐变色彩
- **响应式布局**: 支持不同屏幕尺寸

### 交互设计

- **悬停效果**: 按钮和卡片悬停状态
- **动画效果**: 进度指示器、脉冲动画
- **实时更新**: 图表数据实时刷新
- **筛选功能**: 支持时间周期和任务类型筛选

### 数据可视化

- **ECharts图表**: 柱状图、环形图、进度指示器
- **颜色编码**: 不同状态使用不同颜色区分
- **数据标签**: 清晰的数值和单位显示
- **图例说明**: 详细的图例和说明文字

## 开发指南

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 安装依赖

```bash
pnpm install
```

### 开发模式

```bash
pnpm dev
```

### 构建生产版本

```bash
pnpm build
```

### 预览生产版本

```bash
pnpm preview
```

## 组件开发规范

### 1. 组件命名

- 使用PascalCase命名组件文件
- 组件名应具有描述性，清晰表达功能

### 2. 样式规范

- 使用Tailwind CSS类名
- 复杂样式使用scoped CSS
- 保持一致的间距和颜色规范

### 3. 图表配置

- 使用ECharts配置对象
- 保持图表样式一致性
- 支持响应式调整

### 4. 数据管理

- 使用Vue 3 Composition API
- 组件内部状态管理
- 支持数据更新和刷新

## 性能优化

### 1. 代码分割

- 组件按需加载
- 路由懒加载
- 第三方库按需引入

### 2. 图表优化

- ECharts实例复用
- 窗口大小变化时重新调整
- 组件卸载时清理资源

### 3. 样式优化

- Tailwind CSS按需编译
- 减少不必要的重绘
- 使用CSS硬件加速

## 部署说明

### 1. 构建配置

- Vite生产构建优化
- 静态资源压缩
- 代码分割和懒加载

### 2. 环境变量

- 开发和生产环境配置
- API接口地址配置
- 地图服务配置

### 3. 部署流程

- 构建生产版本
- 静态文件部署
- CDN加速配置

## 维护和更新

### 1. 依赖更新

- 定期更新依赖包
- 检查安全漏洞
- 测试兼容性

### 2. 功能扩展

- 新增图表类型
- 添加数据源
- 优化用户体验

### 3. 性能监控

- 页面加载性能
- 图表渲染性能
- 内存使用情况

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
