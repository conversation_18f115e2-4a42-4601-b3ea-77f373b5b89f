<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
} from '@/components/ui/dialog'
import Map from '@/components/Map.vue'

// 定义组件的 props
const props = defineProps<{
  open: boolean
  data?: any
}>()

// 定义组件的 emits
const emit = defineEmits<{
  (e: 'close'): void
}>()

// 地图组件的引用
const mapRef = ref<InstanceType<typeof Map> | null>(null)

// 企业信息数据
const enterpriseInfo = ref({
  特许经营企业: '河北雄安中石化燃气',
  企业负责人: '张**',
  负责人电话: '19997631216',
  高压管网: '2km',
  中压管网: '13km',
  低压管网: '46km',
  管道气用户: '2001户',
  瓶装气用户: '0户',
  加气站: '0家',
  门站: '1个',
  储备站: '1个',
  调压站: '3个',
  阀门井: '38个',
  售后服务站: '2个',
  用户监测设备: '2090台',
  巡检任务: '1个',
  已完成巡检任务: '1个',
  巡检发现隐患: '2处',
  入户安检: '2090户',
  已入户安检: '1996户',
  入户安检隐患: '56处',
  现有隐患: '1个',
  已解决隐患: '55个',
  隐患处置率: '98.21%',
  一级风险点: '1个',
  二级风险点: '0个',
  三级风险点: '0个',
  四级风险点: '4个',
  老旧管网改造: '0.2km',
  已改造管网: '0.2km',
  改造完成率: '100%',
  在用气瓶: '0个',
  监管单位: '大河镇燃热管理中心',
  监管单位联系人: '王**',
  联系电话: '8888-63694',
})

// 将企业信息数据转换为数组以便于渲染
const infoList = ref<{ label: string; value: string }[][]>([])

const formatInfo = () => {
  const allInfo = Object.entries(enterpriseInfo.value).map(([label, value]) => ({ label, value }))
  const list1: { label: string; value: string }[] = []
  const list2: { label: string; value: string }[] = []
  const list3: { label: string; value: string }[] = []
  allInfo.forEach((item, index) => {
    if (index % 3 === 0) {
      list1.push(item)
    } else if (index % 3 === 1) {
      list2.push(item)
    } else {
      list3.push(item)
    }
  })
  infoList.value = [list1, list2, list3]
}

formatInfo()

// 监听 props.open 的变化
watch(
  () => props.open,
  newVal => {
    if (newVal && mapRef.value && props.data?.area) {
      mapRef.value.drawPolygon(props.data.area)
    }
  },
)

// 地图准备好后的回调
const onMapReady = () => {
  if (props.data?.area) {
    mapRef.value?.drawPolygon(props.data.area)
  }
}

// 处理更新打开状态的事件
const handleUpdateOpen = (open: boolean) => {
  if (!open) {
    mapRef.value?.destroyPolygon()
    emit('close')
  }
}
</script>

<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[1200px] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none"
    >
      <DialogHeader>
        <DialogTitle class="h-12 text-white pl-9 leading-12 title-bg">企业信息</DialogTitle>
        <DialogDescription as="div" class="p-6 text-sm text-white">
          <div class="flex gap-4">
            <!-- Left Map -->
            <div class="w-[400px] h-[306px] bg-black/20 rounded">
              <Map ref="mapRef" :zoom="14" :pitch="20" @map-ready="onMapReady" />
            </div>
            <!-- Right Info -->
            <div class="grid flex-1 grid-cols-3 gap-4">
              <div v-for="(group, index) in infoList" :key="index" class="flex flex-col gap-2">
                <div v-for="item in group" :key="item.label" class="flex">
                  <span class="text-[#99D5FF] w-28 text-right">{{ item.label }}：</span>
                  <span>{{ item.value }}</span>
                </div>
              </div>
            </div>
          </div>
        </DialogDescription>
      </DialogHeader>
    </DialogContent>
  </Dialog>
</template>

<style scoped>
.title-bg {
  background: url('@/assets/dialog/title-bg-1200.png') no-repeat 0 0;
  background-size: cover;
  font-family: MStiffHei PRC;
}
</style>
