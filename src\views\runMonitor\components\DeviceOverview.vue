<template>
  <div class="panel-container-hfHgt-hfWdt">
    <div class="panel-header">设备概况</div>
    <div class="p-4 panel-content">
      <div class="status-indicators">
        <div class="status-item animate-pulse">
          <div class="status-value">169</div>
          <div class="status-label">站点设备数</div>
        </div>
        <div class="status-item animate-pulse">
          <div class="status-value">223</div>
          <div class="status-label">管网设备数</div>
        </div>
        <div class="status-item animate-pulse">
          <div class="status-value">136215</div>
          <div class="status-label">入户设备数</div>
        </div>
      </div>
      <div class="chart-section">
        <div ref="chartRef" class="donut-chart"></div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1

const option = {
    backgroundColor: 'transparent',
    legend: {
      show: true,
      orient: 'vertical',
      right: '0%',
      bottom: '0%',
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      itemWidth: 12,
      itemHeight: 8,
      icon: 'rect',
    },
    series: [
      {
        type: 'pie',
        radius: ['46.1%', '77.5%'],
        center: ['38.2%', '50%'],
        silent: true,
        data: [{ value: 1, itemStyle: { color: 'rgba(153, 213, 255, 0.15)' } }],
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
      },
      {
        type: 'pie',
        radius: ['52.1%', '71.9%'],
        center: ['38.2%', '50%'],
        data: [
          { value: 169, name: '场站设备数', itemStyle: { color: '#99d5ff' } },
          { value: 223, name: '管网设备数', itemStyle: { color: '#E19760' } },
          { value: 13621, name: '入户设备数', itemStyle: { color: '#a3f58f' } },
        ],
        label: {
          show: true,
          color: '#fff',
          formatter: `{percent|{d}%}\n{value|{c}}个`,
          rich: {
            percent: {
              fontSize: 20,
              color: '#fff',
            },
            value: {
              fontSize: 12,
              color: '#fff',
            },
          },
        },
        labelLine: {
          show: true,
        },
      },
    ],
  }

const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startHighlightAnimation()
}

// 开始高亮轮播动画
const startHighlightAnimation = () => {
  const dataCount = option.series[1].data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 1, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 1, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initchart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.panel-content {
  height: calc(100% - 44px);
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-indicators {
  width: 448px;
  height: 104px;
  display: flex;
}

.status-item {
  display: flex;
  width: 33.3%;
  background: url('@/assets/run-monitor/device-overview-icon.png') no-repeat center 14px;
  background-size: 72px;
  flex-direction: column;
  justify-content: space-between;
}

.status-value {
  font-family: NotoSansSC;
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  text-align: center;
  color: #ffffff;
  white-space: nowrap;
}

.status-label {
  font-family: NotoSansSC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  text-align: center;
  letter-spacing: normal;
  color: #ffffff;
}

.chart-section {
  width: 448px;
  height: 258px;
}

.donut-chart {
  width: 100%;
  height: 100%;
}
</style>
