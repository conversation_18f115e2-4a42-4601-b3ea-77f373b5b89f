<template>
  <div class="dashboard-layout">
    <div class="dashboard-content">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <div class="flex gap-6">
          <DeviceOverview />
          <StationDeviceMonitor />
        </div>
        <div class="flex gap-6">
          <UserDeviceAlarm @click="onShowAlarmDetailDialog" />
          <PressWellMonitor />
        </div>
      </div>

      <!-- 中央地图区域 -->
      <div class="center-panel">
        <!-- 地图控制组件 -->
        <LayersTool v-model="layersData" />
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <div class="flex flex-col gap-6">
          <StationOverview />
          <StationMonitoringVideo @click="onShowOnlineVideoDialog" />
          <PipeLeakAlarm @click="onShowPipelineLeakDialog" />
        </div>
        <div class="flex flex-col gap-6">
          <CylinderFillStats />
          <RegulatorStationDeviceMonitor />
        </div>
      </div>
    </div>
    <!-- 管网泄漏 -->
    <PipelineLeakDialog
      :open="showPipelineLeakDialog"
      :data="pipelineLeakInfo"
      @close="showPipelineLeakDialog = false"
    />
    <!-- 在线视频 -->
    <VideoDialog :open="showVideoDialog" @close="showVideoDialog = false" :data="videoInfo" />
    <!-- 在线视频 -->
    <AlarmDetailDialog :open="showAlarmDetailDialog" @close="showAlarmDetailDialog = false" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import LayersTool from '@/components/LayersTool.vue'
import DeviceOverview from './components/DeviceOverview.vue'
import StationDeviceMonitor from './components/StationDeviceMonitor.vue'
import UserDeviceAlarm from './components/UserDeviceAlarm.vue'
import PressWellMonitor from './components/PressWellMonitor.vue'
import StationOverview from './components/StationOverview.vue'
import StationMonitoringVideo from './components/StationMonitoringVideo.vue'
import PipeLeakAlarm from './components/PipeLeakAlarm.vue'
import RegulatorStationDeviceMonitor from './components/RegulatorStationDeviceMonitor.vue'
import CylinderFillStats from './components/CylinderFillstats.vue'

import PipelineLeakDialog from '../emergency/PipelineLeakDialog.vue' // 管网泄漏弹窗演示复用应急页面
import VideoDialog from '../emergency/VideoDialog.vue' // 在线视频弹窗演示复用应急页面
import AlarmDetailDialog from './AlarmDetailDialog.vue'
import { layerData } from './layerData'

const showPipelineLeakDialog = ref<boolean>(false)
const showVideoDialog = ref<boolean>(false)
const showAlarmDetailDialog = ref<boolean>(false)
const pipelineLeakInfo = ref<any>({})
const videoInfo = ref<any>({
  id: '1',
  title: '--',
  address: '--',
  url: '',
})
// 图层数据
const layersData = ref<any>(layerData)

const onShowPipelineLeakDialog = (record: any) => {
  console.log('Pipeline leak record:', record)
  pipelineLeakInfo.value = record
  showPipelineLeakDialog.value = true
}
const onShowAlarmDetailDialog = () => {
  // 可以在这里处理弹窗显示逻辑
  showAlarmDetailDialog.value = true
}
const onShowOnlineVideoDialog = (video: any) => {
  console.log('Online video clicked:', video)
  videoInfo.value = video
  // 可以在这里处理弹窗显示逻辑
  showVideoDialog.value = true
}
</script>

<style scoped>
.dashboard-layout {
  position: absolute;
  top: 121px;
  left: 24px;
  right: 24px;
  height: calc(100% - 121px);
  overflow: hidden;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 350px 1fr 350px;
  gap: 24px;
  padding: 24px;
  padding-top: 120px;
  width: 100%;
  height: 100%;
  margin: 0 auto;
}
.left-panel,
.right-panel {
  position: absolute;
  top: 0;
  width: 984px;
}
.left-panel {
  left: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.right-panel {
  right: 0;
  display: flex;
  flex-direction: row;
  gap: 24px;
  z-index: 10;
}

.center-panel {
  position: absolute;
  top: 0;
  left: 1008px;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    padding: 20px;
    padding-top: 120px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 280px 1fr 280px;
    gap: 16px;
    padding: 16px;
    padding-top: 120px;
  }
}
</style>
