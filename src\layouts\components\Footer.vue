<template>
  <div class="wrapper">
    <Carousel class="content" :opts="{ align: 'start' }">
      <CarouselContent class="flex gap-2 ml-2">
        <CarouselItem
          v-for="item in eventList"
          :key="item.id"
          :class="['scroll-item', 'flex-none', { 'scroll-item-active': activeId === item.id }]"
          @click="handleClick(item)"
        >
          <div class="flex">
            <img :src="item.img" alt="图片" class="w-[96px] h-[72px] object-cover" />
            <div class="flex flex-col pl-2 grow">
              <p class="text-xs">{{ item.title }}</p>
              <p class="w-full text-[8px]">时间：{{ item.time }}</p>
              <p class="w-full h-6 text-[8px] overflow-hidden">位置：{{ item.address }}</p>
              <p
                :class="[
                  'text-[8px]',
                  'status-tag',
                  { finished: item.status === 2, pending: item.status === 1, waiting: item.status === 0 },
                ]"
              >
                {{ statusMap[item.status] }}
              </p>
            </div>
          </div>
        </CarouselItem>
      </CarouselContent>
      <CarouselPrevious class="scroll-btn left-btn" />
      <CarouselNext class="scroll-btn right-btn" />
    </Carousel>
  </div>
  <PipelineLeakDialog :open="showPipelineLeakDialog" @close="showPipelineLeakDialog = false" />
  <AlarmDetailDialog
    :open="showAlarmDetailDialog"
    @close="showAlarmDetailDialog = false"
    :data="alarmDetailInfo"
    @success="handleResolve"
  />
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { sendToUe } from '@/utils/ue'
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel'
import GasLeakImg from '@/assets/footer/gas-leak.png'
import PipelineLeakDialog from '@/views/emergency/PipelineLeakDialog.vue'
import AlarmDetailDialog from '@/views/runMonitor/AlarmDetailDialog.vue'
import { WebSocketService } from '@/utils/websocket'
import type { AlarmInfo } from '@/types/alarmDialog.ts'
import type { EventInfo } from '@/types/footer.ts'

// 接收传入事件
const emit = defineEmits<{
  (e: 'onWarning', value: { isShow: boolean; id: string }): void
}>()

const statusMap: Record<number, string> = {
  0: '待处理',
  1: '处理中',
  2: '已处理',
}
const eventList = ref<EventInfo[]>([
  {
    title: '居民燃气泄漏',
    time: '2025-02-30 24:59:59',
    address: '电信产业园B1大厅',
    img: GasLeakImg,
    status: 1,
    id: '0',
    location: { x: -201240, y: 40920, z: 10 },
    type: 0,
    scale: 5,
  },
  {
    title: '居民燃气泄漏',
    time: '2025-02-30 24:59:59',
    address: '事故地址',
    img: GasLeakImg,
    status: 2,
    id: '1',
    location: { x: -201240, y: 40920, z: 10 },
    type: 0,
    scale: 5,
  },
  {
    title: '管道燃气泄漏',
    time: '2025-02-30 24:59:59',
    address: '事故地址',
    img: GasLeakImg,
    status: 2,
    id: '2',
    location: { x: -201240, y: 40920, z: 10 },
    type: 1,
    scale: 5,
  },
  {
    title: '管道燃气泄漏',
    time: '2025-02-30 24:59:59',
    address: '事故地址',
    img: GasLeakImg,
    status: 2,
    id: '3',
    location: { x: -201240, y: 40920, z: 10 },
    type: 1,
    scale: 5,
  },
  {
    title: '居民燃气泄漏',
    time: '2025-02-30 24:59:59',
    address: '事故地址',
    img: GasLeakImg,
    status: 2,
    id: '4',
    location: { x: -201240, y: 40920, z: 10 },
    type: 0,
    scale: 5,
  },
  {
    title: '居民燃气泄漏',
    time: '2025-02-30 24:59:59',
    address: '事故地址',
    img: GasLeakImg,
    status: 2,
    id: '5',
    location: { x: -201240, y: 40920, z: 10 },
    type: 0,
    scale: 5,
  },
  {
    title: '居民燃气泄漏',
    time: '2025-02-30 24:59:59',
    address: '事故地址',
    img: GasLeakImg,
    status: 2,
    id: '6',
    location: { x: -201240, y: 40920, z: 10 },
    type: 0,
    scale: 5,
  },
])
const activeId = ref<null | string>(null)
const showPipelineLeakDialog = ref(false)
const showAlarmDetailDialog = ref(false)
const alarmDetailInfo = ref<AlarmInfo | null>(null)

// WebSocket 服务实例
let wsService: WebSocketService | null = null

// 处理点击事件
const handleClick = (record: any) => {
  activeId.value = record.id

  // 移除上一个点的特效
  sendToUe({
    command: 'RemoveParticel',
    args: {},
  })

  const selectedEvent = eventList.value.find(item => item.id === record.id)
  if (selectedEvent && selectedEvent.location) {
    sendToUe({
      command: 'MoveTo',
      args: {
        location: selectedEvent.location,
        distance: 50000.0,
      },
    })
    // 特效
    sendToUe({
      command: 'AddParticel',
      args: {
        id: selectedEvent.id,
        location: selectedEvent.location,
        type: selectedEvent.type,
        scale: selectedEvent.scale || 1.0,
      },
    })
  }
  // 如果是管道燃气泄漏，打开管道泄漏弹窗
  if (record.type === 1) {
    showPipelineLeakDialog.value = true
  } else {
    // 居民泄漏弹窗
    alarmDetailInfo.value = record
    showAlarmDetailDialog.value = true
  }
}

// 处置完成
const handleResolve = () => {
  showAlarmDetailDialog.value = false
  // 更新事件列表
  eventList.value = eventList.value.map(item => {
    if (item.id === alarmDetailInfo.value?.id) {
      return {
        ...item,
        status: 2, // 已处理
      }
    }
    return item
  })
  // 关闭告警
  emit('onWarning', { id: alarmDetailInfo.value?.id || '', isShow: false })
}

// 连接ws告警
const connectWS = () => {
  const wsUrl = 'wss://city189.cn:3610/ctwing/websocket/alarm'
  wsService = new WebSocketService(wsUrl)

  // 设置消息处理器
  wsService.onMessage = data => {
    console.log('接收到 WebSocket 消息:', data)
    // 检查是否是报警状态
    if (data.state === '报警') {
      // 创建新的事件对象
      const newEvent = {
        title: '居民燃气泄漏',
        time: new Date(data.timestamp).toLocaleString('zh-CN'),
        address: data.address || '电信产业园B1大厅',
        img: GasLeakImg,
        status: 0, // 待处理
        id: data.deviceId, // 使用deviceId作为唯一ID
        location: { lng: 115.951257, lat: 39.025119 }, // 默认位置
        type: 0, // 居民燃气泄漏
        scale: 5,
      }

      // 将新事件添加到列表开头
      eventList.value.unshift(newEvent)

      // 触发全屏警报
      emit('onWarning', { id: newEvent.id, isShow: true })

      // 10秒后自动关闭警报
      // setTimeout(() => {
      //   emit('onWarning', { id: '', isShow: false })
      // }, 10000)
    }
  }

  // 连接 WebSocket
  wsService.connect()
}

onMounted(() => {
  connectWS()
})

onUnmounted(() => {
  // 组件卸载时断开 WebSocket 连接
  if (wsService) {
    wsService.disconnect()
  }
})
</script>
<style scoped>
.wrapper {
  position: absolute;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  width: 1128px;
  height: 104px;
  z-index: 10;
  background: url('@/assets/footer/footer-bg.png') no-repeat center;
  background-size: 100% 100%;
  backdrop-filter: blur(4px);
}
.content {
  position: relative;
  margin: 0 auto;
  width: 1104px;
  height: 100%;
  padding: 8px 32px;
  box-sizing: border-box;
  background: rgba(42, 56, 77, 0.6);
}
.scroll-btn {
  width: 16px;
  height: 16px;
  cursor: pointer;
  background-size: 100% 100%;
  border: none;
}
.left-btn {
  left: 8px;
  background: url(@/assets/footer/scroll-icon.png) no-repeat center;
}
.right-btn {
  transform: rotateZ(180deg);
  transform-origin: center;
  right: 8px;
  background: url(@/assets/footer/scroll-icon.png) no-repeat center;
}
.scroll-item {
  padding: 8px;
  width: 225px;
  height: 88px;
  color: #fff;
  border: 1px solid #99d5ff;
  border-radius: 2px;
  box-sizing: border-box;
}
.scroll-item-active {
  border: 1px solid #ff6666;
  box-shadow: inset 0px 0px 9.96px 0px rgba(255, 102, 102, 0.6);
}
.status-tag {
  margin-top: auto;
  width: 40px;
  height: 16px;
  line-height: 14px;
  text-align: center;
  border-radius: 2px;
  box-sizing: border-box;
}
.finished {
  background: rgba(163, 245, 143, 0.3);
  border: 0.62px solid #a3f58f;
}
.pending {
  background: rgba(153, 213, 255, 0.3);
  border: 0.62px solid #99d5ff;
}
.waiting {
  background: rgba(255, 102, 102, 0.3);
  border: 0.62px solid #ff6666;
}
</style>
