<template>
  <div class="header-wrapper">
    <div class="flex justify-between header-content">
      <!-- <div class="title">城市生命线燃气安全感知运营平台</div> -->
      <div class="flex items-center gap-2 location">
        <div class="nav-icon"></div>
        雄安新区
      </div>
      <div class="nav-tabs">
        <div class="nav-item" @click="navigateTo('/gas')" :class="{ active: currentPath === '/gas' }">
          <div class="nav-item-text">态势感知</div>
        </div>
        <!-- <div class="nav-item">
          <div class="nav-item-text">
            行业监管
          </div>
        </div> -->
        <div class="nav-item" @click="navigateTo('/runMon')" :class="{ active: currentPath === '/runMon' }">
          <div class="nav-item-text">运行监测</div>
        </div>
        <!-- <div class="nav-item">
          <div class="nav-item-text">
            风险隐患
          </div>
        </div>
        <div class="nav-item">
          <div class="nav-item-text">
            监督检查
          </div>
        </div> -->
        <div class="title-placeholder"></div>
        <div
          class="transform nav-item -scale-x-100"
          @click="navigateTo('/emergency')"
          :class="{ active: currentPath === '/emergency' }"
        >
          <div class="transform nav-item-text -scale-x-100">应急管理</div>
        </div>
        <!-- <div class="transform nav-item -scale-x-100">
          <div class="transform nav-item-text -scale-x-100">
            安全评价
          </div>
        </div>
        <div class="transform nav-item -scale-x-100">
          <div class="transform nav-item-text -scale-x-100">
            服务监督
          </div>
        </div>
        <div class="transform nav-item -scale-x-100">
          <div class="transform nav-item-text -scale-x-100">
            培训提升
          </div>
        </div> -->
        <div
          class="transform nav-item -scale-x-100"
          @click="navigateTo('/hazard')"
          :class="{ active: currentPath === '/hazard' }"
        >
          <div class="transform nav-item-text -scale-x-100">重大危险源</div>
        </div>
      </div>
      <div class="flex items-center gap-2 exit-btn">
        <div class="nav-icon"></div>
        进入后台
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const currentPath = ref(route.path)
watch(
  () => route.path,
  newPath => {
    currentPath.value = newPath
  },
)

const router = useRouter()
const navigateTo = (path: string) => {
  router.push({ path })
}

// const activeTab = ref('smart-gas')
</script>

<style scoped>
.header-wrapper {
  width: 100%;
  height: 98px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  background: url('@/assets/header/header-bg.png') no-repeat center;
  background-size: 100% 100%;
  backdrop-filter: blur(4px);
}

.header-content {
  height: 100%;
  padding: 0 40px;
}

.title {
  font-size: 32px;
  font-weight: bold;
  color: white;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.title-placeholder {
  width: 1136px;
  height: 100%;
  font-family: MStiffHei PRC;
  font-size: 44px;
  font-weight: normal;
  line-height: 52px;
  text-align: center;
}

.location,
.exit-btn {
  font-family: Noto Sans SC;
  font-size: 24px;
  font-weight: bold;
  line-height: 32px;
  color: #99d5ff;
  cursor: pointer;
}

.nav-icon {
  width: 32px;
  height: 32px;
}

.location .nav-icon {
  background: url('@/assets/header/location-icon.png') no-repeat center;
  background-size: 100% 100%;
}

.exit-btn .nav-icon {
  background: url('@/assets/header/exit-icon.png') no-repeat center;
  background-size: 100% 100%;
}

.nav-tabs {
  display: flex;
  align-items: center;
}

.nav-item {
  position: relative;
  width: 228px;
  height: 56px;
  font-family: Noto Sans SC;
  font-size: 24px;
  font-weight: bold;
  line-height: 56px;
  text-align: center;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  background: url('@/assets/header/page-btn.png') no-repeat center;
  background-size: 100% 100%;
}

.nav-item:hover {
  filter: brightness(1.2);
  color: white;
}

.nav-item.active {
  background: url('@/assets/header/page-btn-active.png') no-repeat center;
  background-size: 100% 100%;
}
</style>
