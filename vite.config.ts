import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': '/src',
    },
  },
  // 开发服务器配置
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://10.6.5.245:80',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api/, ''),
      },
      '/prod-api': {
        target: 'https://city189.cn:3600/gasPlatform',
        changeOrigin: true,
        secure: false,
        rewrite: path => path.replace(/^\/prod-api/, ''),
      },
    },
  },
})
