<template>
  <div class="panel-container-half">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">站点监控画面</div>
      <div class="header-dropdown">
        <AsyncSearchCombobox
          v-model="selectedOpt"
          :options="options"
          placeholder="搜索站点..."
          no-results-text="无搜索结果"
          :search-fn="searchStations"
          :debounce-ms="300"
          @search="handleSearch"
        />
      </div>
    </div>
    <div class="p-4 panel-content">
      <div class="relative object-fill w-full h-full overflow-hidden rounded" @click="handleShowDialog(videoInfo)">
        <video
          class="object-fill w-full h-full"
          :src="videoInfo.url"
          disablepictureinpicture
          muted
          loop
          autoplay
          playsinline
        ></video>
        <div class="absolute bottom-0 flex items-center video-label flex-start">
          <MapPin class="w-4 h-4 ml-3" />
          <div class="ml-1 text-xs">{{ videoInfo.title }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import { MapPin } from 'lucide-vue-next'
import { AsyncSearchCombobox } from '@/components/ui/combobox'

const emit = defineEmits<{
  (e: 'click', value: any): void
}>()

const selectedOpt = ref<string>('1')

// 模拟异步搜索函数
const searchStations = async (query: string): Promise<{ label: string; value: string }[]> => {
  // 模拟API请求延迟
  await new Promise(resolve => setTimeout(resolve, 500))

  // 如果查询为空，返回所有选项
  if (!query) return options.value

  // 过滤匹配的选项
  return options.value.filter(option => option.label.toLowerCase().includes(query.toLowerCase()))
}

// 处理搜索事件
const handleSearch = (query: string) => {
  console.log('搜索查询:', query)
}

// 站点选项数据
const options = ref([
  { label: '华润燃气东门路口', value: '1' },
  { label: '兴达街路口东北角', value: '2' },
  { label: '中燃能源调压站正门', value: '3' },
])

const videoList = [
  {
    id: '1',
    title: '华润燃气东门路口',
    address: '东门路口',
    url: '/mock/stream.webm',
  },
  {
    id: '2',
    title: '兴达街路口东北角',
    address: '兴达街路口东北角',
    url: '/mock/stream2.webm',
  },
  {
    id: '3',
    title: '中燃能源调压站正门',
    address: '中燃能源调压站正门',
    url: '/mock/stream3.webm',
  },
]

// 监听选中值变化，更新视频信息
const videoInfo = ref<any>(
  videoList.find(v => v.id === selectedOpt.value) || {
    id: '1',
    title: '华润燃气东门路口',
    address: '东门路口',
    url: '/mock/stream.webm',
  },
)

// 监听选中值变化
watch(selectedOpt, newValue => {
  const selectedVideo = videoList.find(v => v.id === newValue)
  if (selectedVideo) {
    videoInfo.value = selectedVideo
  }
})

const handleShowDialog = (video: any) => {
  emit('click', video)
}
</script>

<style scoped>
@import '@/styles/index.css';

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.panel-content {
  height: calc(100% - 44px);
}

.video-label {
  width: 448px;
  height: 24px;
  background: rgba(16, 34, 54, 0.6);
}
</style>
