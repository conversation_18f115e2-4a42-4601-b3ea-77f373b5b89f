<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const activeTab = ref<'monthly' | 'quarterly'>('monthly')
const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1

const option = {
  backgroundColor: 'transparent',
  grid: {
    left: '2%',
    right: '0%',
    top: '15%',
    bottom: '0%',
    containLabel: true,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    textStyle: {
      color: '#fff',
    },
    backgroundColor: 'rgba(42, 56, 77, 0.6)',
    borderWidth: 0,
  },
  xAxis: {
    type: 'category',
    data: ['2000.1', '2000.2', '20000.3', '2000.4', '2000.5', '2000.6', '2000.7', '2000.8'],
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.3)',
      },
    },
    axisLabel: {
      color: 'rgba(255, 255, 255, 0.8)',
      fontSize: 10,
    },
    axisTick: {
      show: false,
    },
  },
  yAxis: {
    type: 'value',
    name: '单位: 起',
    nameTextStyle: {
      color: 'rgba(255, 255, 255, 0.8)',
      fontSize: 10,
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.3)',
      },
    },
    axisLabel: {
      color: 'rgba(255, 255, 255, 0.8)',
      fontSize: 10,
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.1)',
        type: 'dashed',
      },
    },
  },
  legend: {
    show: false,
    // data: ['现存风险', '现存隐患'],
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    itemWidth: 20,
    itemHeight: 10,
    icon: 'rect',
    itemGap: 24,
    top: 0,
  },
  series: [
    {
      name: '泄漏发生',
      type: 'line',
      data: [5, 4, 5, 3, 3, 4, 2, 4],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(153, 213, 255, 0.6)' },
          { offset: 1, color: 'rgba(153, 213, 255, 0.15)' },
        ]),
      },
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = option.xAxis.data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>
<template>
  <div class="panel-container-half">
    <div class="panel-header">管道泄漏发生统计</div>
    <div class="relative p-4 panel-content">
      <div class="absolute z-10 flex justify-end w-full gap-2 top-4 right-4">
        <div
          class="w-16 h-6 text-center text-xs leading-6 border box-border border-[#99D5FF] bg-[#99D5FF]/15 cursor-pointer"
          :class="{ 'bg-[#FFD966]/15 border-[#FFD966]': activeTab === 'monthly' }"
          @click="activeTab = 'monthly'"
        >
          月度
        </div>
        <div
          class="w-16 h-6 text-center text-xs leading-6 border box-border border-[#99D5FF] bg-[#99D5FF]/15 cursor-pointer"
          :class="{ 'bg-[#FFD966]/15 border-[#FFD966]': activeTab === 'quarterly' }"
          @click="activeTab = 'quarterly'"
        >
          季度
        </div>
      </div>
      <div ref="chartRef" class="chart"></div>
    </div>
  </div>
</template>
<style scoped>
@import '@/styles/index.css';

.chart {
  width: 100%;
  height: 216px;
}
</style>
