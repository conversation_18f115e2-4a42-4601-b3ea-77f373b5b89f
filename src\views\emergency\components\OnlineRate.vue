<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-liquidfill'

const deviceOnlineRateChartRef = ref<HTMLElement>()
let deviceOnlineRateChart: echarts.ECharts | null = null
const online = ref(75)
const offline = ref(25)

const initChart = () => {
  if (!deviceOnlineRateChartRef.value) return

  deviceOnlineRateChart = echarts.init(deviceOnlineRateChartRef.value)

  const option = {
    series: [
      {
        type: 'liquidFill',
        data: [
          {
            value: 0.75,
            phase: 0, // 第一个波浪的相位
            direction: 'right',
          },
          {
            value: 0.75,
            phase: Math.PI, // 第二个波浪相位差180度
            direction: 'left',
          },
        ], // 多个数值产生波浪效果
        radius: '100%',
        color: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(140, 255, 255, 0.8)',
              },
              {
                offset: 1,
                color: 'rgba(140, 255, 255, 0)',
              },
            ],
          },
        ],
        backgroundStyle: {
          color: 'rgba(140, 255, 255, 0.3)',
        },
        outline: {
          show: false,
        },
        label: {
          position: ['50%', '50%'],
          formatter: '75%',
          fontSize: 28,
          color: '#fff',
          fontWeight: 'bold',
        },
        amplitude: '8%', // 波浪振幅
        waveLength: '80%', // 波长
        phase: 0, // 相位自动
        period: 3000, // 动画周期
        direction: 'right', // 波浪方向
      },
    ],
  }

  deviceOnlineRateChart.setOption(option)
}

// 监听数据变化
watch([online, offline], ([newOnline, newOffline]) => {
  const total = newOnline + newOffline
  const rate = total ? newOnline / total : 0

  deviceOnlineRateChart?.setOption({
    series: [
      {
        data: [
          {
            value: rate,
            phase: 0, // 第一个波浪的相位
            direction: 'right',
          },
          {
            value: rate,
            phase: Math.PI, // 第二个波浪相位差180度
            direction: 'left',
          },
        ],
        label: {
          formatter: `${Math.round(rate * 100)}%`,
        },
      },
    ],
  })
})

// 自适应大小
const handleResize = () => {
  deviceOnlineRateChart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  deviceOnlineRateChart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>
<template>
  <div class="panel-container-half">
    <div class="panel-header">应急监控实时在线率</div>
    <div class="flex items-center justify-around p-4 panel-content">
      <div ref="deviceOnlineRateChartRef" class="chart"></div>
      <div class="flex flex-col justify-center h-full gap-3">
        <div class="flex items-center justify-center flex-none gap-3 ind-online">
          <div class="online-left">
            <span class="w-7 h-7 indicator-icon"></span>
          </div>
          <div>
            <span class="text-[#8CFFFF] text-sm">监控在线数</span>
            <p>
              <span class="text-xl font-bold text-white">{{ online }}</span>
              <span class="text-sm text-white">个</span>
            </p>
          </div>
        </div>
        <div class="flex items-center justify-center flex-none gap-3 ind-offline">
          <div class="offline-left">
            <span class="w-7 h-7 indicator-icon"></span>
          </div>
          <div>
            <span class="text-[#FF6666] text-sm">监控离线数</span>
            <p>
              <span class="text-xl font-bold text-white">{{ offline }}</span>
              <span class="text-sm text-white">个</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
@import '@/styles/index.css';

.ind-online {
  width: 168px;
  height: 80px;
  background: url('@/assets/emergency/online-bg.png') no-repeat center;
  background-size: cover;
}
.ind-offline {
  width: 168px;
  height: 80px;
  background: url('@/assets/emergency/offline-bg.png') no-repeat center;
  background-size: cover;
}
.online-left {
  width: 56px;
  height: 48px;
  display: flex;
  justify-content: center;
  background: url('@/assets/emergency/online-icon-bg.png') no-repeat center bottom;
  background-size: 56px 30px;
}
.offline-left {
  width: 56px;
  height: 48px;
  display: flex;
  justify-content: center;
  background: url('@/assets/emergency/offline-icon-bg.png') no-repeat center bottom;
  background-size: 56px 30px;
}
.indicator-icon {
  display: inline-block;
  width: 28px;
  height: 28px;
  background: url('@/assets/emergency/ind-1-1.png') no-repeat center;
  background-size: contain;
}
.chart {
  position: relative;
  width: 144px;
  height: 144px;

  &::after {
    content: '';
    display: block;
    width: 154px;
    height: 154px;
    border: 2px solid rgba(140, 255, 255, 0.5);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 1;
  }
}
</style>
