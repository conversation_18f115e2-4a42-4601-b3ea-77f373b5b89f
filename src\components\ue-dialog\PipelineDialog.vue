<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogHeader,
  DialogTitle,
  // DialogClose,
} from '@/components/ui/dialog'

const props = defineProps<{
  open: boolean
  data?: {
    name: string
    pipeType: string
    industry: string
    material: string
    burialDepth: string
    commissioningTime: string
    node: string
    personInCharge: string
    phone: string
  } | null
}>()
const emit = defineEmits<{
  (e: 'close'): void
}>()

const data = ref(
  props.data ?? {
    name: 'G01-G02',
    pipeType: '高压管网/中压/低压',
    industry: '河北雄安中石油昆仑燃气',
    material: '铸铁',
    burialDepth: '3.2米',
    commissioningTime: '2007年7月-3日',
    node: 'G01',
    // node: 'G01-G02',
    personInCharge: '张*',
    phone: '19997631216',
  },
)

watch(
  () => props.data,
  newData => {
    if (newData) {
      data.value = newData
    }
  },
)
// 处理更新打开状态的事件
const handleUpdateOpen = (open: boolean) => {
  // 当接收到 open 为 false 的更新时，触发 close 事件
  if (!open) {
    emit('close')
  }
}
</script>
<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[400px] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none"
    >
      <DialogHeader>
        <DialogTitle class="h-12 text-white pl-9 leading-12 title-bg">管网详情</DialogTitle>
        <DialogDescription as="div" class="p-6 text-sm text-white">
          <div class="flex flex-col gap-5">
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">管段名称：</span>
              {{ data.name }}
            </p>
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">管网性质：</span>
              {{ data.pipeType }}
            </p>
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">所属企业：</span>
              {{ data.industry }}
            </p>
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">管段材质：</span>
              {{ data.material }}
            </p>
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">埋深：</span>
              {{ data.burialDepth }}
            </p>
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">投用时间：</span>
              {{ data.commissioningTime }}
            </p>
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">管段节点：</span>
              {{ data.node }}
            </p>
            <!-- <p class="flex"><span class="block w-[8em] text-right text-[#99D5FF]">管段节点：</span>{{ data.node }}</p> -->
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">企业负责人：</span>
              {{ data.personInCharge }}
            </p>
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">负责人电话：</span>
              {{ data.phone }}
            </p>
          </div>
        </DialogDescription>
      </DialogHeader>
    </DialogContent>
  </Dialog>
</template>
<style scoped>
.title-bg {
  background:
    url('@/assets/dialog/title-bg-400.png') no-repeat 0 0,
    linear-gradient(270deg, rgba(71, 235, 235, 0) 0%, rgba(71, 235, 235, 0) 60%, rgba(71, 235, 235, 0.3) 100%),
    linear-gradient(90deg, rgba(119, 168, 217, 0.3) 5%, rgba(105, 141, 191, 0.3) 80%, rgba(105, 141, 191, 0.3) 100%);
  background-size: cover;
  font-family: MStiffHei PRC;
}
</style>
