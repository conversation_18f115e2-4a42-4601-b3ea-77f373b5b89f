// /src/utils/ue.ts

/**
 * @description 定义与UE通信的命令结构
 */
interface UeCommand {
  command: string
  args: Record<string, any>
}

/**
 * @description 向UE发送命令的函数
 * @param command {UeCommand} - 要发送的命令对象
 */
export const sendToUe = (command: UeCommand) => {
  // 检查UE接口是否存在
  if (window.ue && window.ue.interface && typeof window.ue.interface.broadcast === 'function') {
    // 调用UE提供的接口发送消息
    window.ue.interface.broadcast(command)
  } else {
    // 如果接口不存在，则在控制台打印错误，方便调试
    console.error('UE interface is not available. Command:', command)
  }
}

/**
 * @description 定义全局 window 对象上的 ue 属性
 */
declare global {
  interface Window {
    ue: {
      interface: {
        broadcast: (command: UeCommand) => void
        clickpoint?: (data: { id: string }) => void
        clickline?: (data: { id: string }) => void
        clickplant?: (data: { id: string }) => void
      }
    }
  }
}
