<template>
  <div class="panel-container-hfHgt-hfWdt">
    <div class="panel-header">重大危险源比重</div>
    <div class="p-4 panel-content">
      <div class="status-indicators">
        <div class="status-item">
          <div class="status-value">12.5%</div>
          <div class="status-label">一级重大危险源</div>
        </div>
        <div class="status-item">
          <div class="status-value">5%</div>
          <div class="status-label">二级重大危险源</div>
        </div>
        <div class="status-item">
          <div class="status-value">25%</div>
          <div class="status-label">三级重大危险源</div>
        </div>
        <div class="status-item">
          <div class="status-value">12.5%</div>
          <div class="status-label">四级重大危险源</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts"></script>

<style scoped>
@import '@/styles/index.css';

.status-indicators {
  width: 448px;
  height: 376px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.status-item {
  display: flex;
  width: 216px;
  height: 180px;
  background: url('@/assets/hazard/hazard-ratio-icon.svg') no-repeat center 17px;
  background-size: 123px;
  flex-direction: column;
  justify-content: space-between;
}

.status-value {
  margin-top: 61px;
  font-family: Noto Sans SC;
  font-size: 24px;
  font-weight: bold;
  line-height: 32px;
  text-align: center;
  color: #ffffff;
  white-space: nowrap;
}

.status-label {
  margin-bottom: 13px;
  font-family: NotoSansSC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  text-align: center;
  letter-spacing: normal;
  color: #ffffff;
}
</style>
